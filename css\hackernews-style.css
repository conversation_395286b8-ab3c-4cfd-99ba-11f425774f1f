/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Top Banner */
.top-banner {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 8px 0;
    font-size: 13px;
}

.banner-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.banner-text {
    font-weight: 600;
    color: #495057;
}

.social-follow {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #6c757d;
}

.social-link {
    color: #6c757d;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.social-link:hover {
    color: #007bff;
}

/* Header */
.header {
    background: #fff;
    border-bottom: 1px solid #e9ecef;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.logo-text {
    font-size: 24px;
    font-weight: 800;
    display: flex;
    align-items: center;
    gap: 2px;
}

.logo-cyber {
    color: #007bff;
}

.logo-finance {
    color: #212529;
}

.logo-tagline {
    color: #6c757d;
    font-weight: 600;
    font-size: 20px;
    margin-left: 4px;
}

.nav-desktop {
    display: flex;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: #495057;
    font-weight: 500;
    font-size: 14px;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #007bff;
}

.nav-link i {
    margin-right: 5px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.subscribe-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.subscribe-btn:hover {
    background: #0056b3;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #495057;
}

/* Main Navigation */
.main-nav {
    border-top: 1px solid #e9ecef;
    padding: 0;
}

.main-nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-nav-menu li {
    border-right: 1px solid #e9ecef;
}

.main-nav-menu li:last-child {
    border-right: none;
}

.main-nav-menu .nav-link {
    display: block;
    padding: 15px 20px;
    color: #495057;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
}

.main-nav-menu .nav-link:hover,
.main-nav-menu .nav-link.active {
    background: #f8f9fa;
    color: #007bff;
}

/* Mobile Menu */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 300px;
    height: 100vh;
    background: #fff;
    z-index: 2000;
    transition: right 0.3s ease;
    overflow-y: auto;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu-content {
    padding: 20px;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.mobile-menu-header span {
    font-size: 18px;
    font-weight: 600;
}

.mobile-menu-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
}

.mobile-nav ul {
    list-style: none;
    margin-bottom: 30px;
}

.mobile-nav li {
    border-bottom: 1px solid #f8f9fa;
}

.mobile-nav a {
    display: block;
    padding: 12px 0;
    color: #495057;
    text-decoration: none;
    font-weight: 500;
}

.mobile-nav a:hover {
    color: #007bff;
}

.mobile-menu-footer h4 {
    font-size: 14px;
    font-weight: 600;
    margin: 20px 0 10px 0;
    color: #495057;
}

.mobile-menu-footer ul {
    list-style: none;
    margin-bottom: 15px;
}

.mobile-menu-footer li {
    margin-bottom: 5px;
}

.mobile-menu-footer a {
    color: #6c757d;
    text-decoration: none;
    font-size: 13px;
}

.mobile-menu-footer a:hover {
    color: #007bff;
}

.mobile-menu-footer p {
    font-size: 13px;
    color: #6c757d;
    line-height: 1.5;
    margin-bottom: 15px;
}

.social-links {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.social-links a {
    color: #6c757d;
    font-size: 16px;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #007bff;
}

.rss-email {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.rss-email a {
    color: #6c757d;
    text-decoration: none;
    font-size: 13px;
}

.rss-email a:hover {
    color: #007bff;
}

/* Main Content Layout */
.main-content {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
    gap: 40px;
}

.articles-grid {
    flex: 1;
}

/* Placeholder Images */
.placeholder-img {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 16px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.placeholder-img::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
    z-index: 1;
}

.placeholder-img::after {
    content: attr(data-text);
    position: relative;
    z-index: 2;
    padding: 20px;
}

.placeholder-img.small {
    height: 80px;
    font-size: 24px;
}

.placeholder-img.small::after {
    padding: 10px;
}

.placeholder-img.tiny {
    width: 60px;
    height: 60px;
    font-size: 18px;
    border-radius: 4px;
}

.placeholder-img.tiny::after {
    padding: 5px;
}

/* Featured Banner */
.featured-banner {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
}

.banner-img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

/* Page Title */
.page-title {
    margin-bottom: 30px;
}

.page-title h1 {
    font-size: 28px;
    font-weight: 700;
    color: #212529;
    line-height: 1.3;
}

/* Articles */
.main-article {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #e9ecef;
}

.main-article .article-image {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
}

.main-article .article-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
}

.article-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 13px;
}

.article-date {
    color: #6c757d;
}

.article-category {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.main-article .article-title {
    font-size: 24px;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 15px;
}

.main-article .article-title a {
    color: #212529;
    text-decoration: none;
    transition: color 0.3s ease;
}

.main-article .article-title a:hover {
    color: #007bff;
}

.article-excerpt {
    color: #6c757d;
    line-height: 1.6;
    font-size: 15px;
}

/* Secondary Articles */
.secondary-article {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    padding-bottom: 25px;
    border-bottom: 1px solid #f8f9fa;
}

.secondary-article .article-image {
    flex-shrink: 0;
    width: 120px;
    height: 80px;
    border-radius: 6px;
    overflow: hidden;
}

.secondary-article .article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.secondary-article .article-content {
    flex: 1;
}

.secondary-article .article-title {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 8px;
}

.secondary-article .article-title a {
    color: #212529;
    text-decoration: none;
    transition: color 0.3s ease;
}

.secondary-article .article-title a:hover {
    color: #007bff;
}

.secondary-article .article-meta {
    margin-bottom: 8px;
}

/* Load More */
.load-more {
    text-align: center;
    margin-top: 40px;
}

.load-more-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 5px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
}

.load-more-btn:hover {
    background: #0056b3;
}

/* Sidebar */
.sidebar {
    width: 300px;
    flex-shrink: 0;
}

.sidebar-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
}

.sidebar-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #212529;
}

/* Trending Articles */
.trending-item {
    display: flex;
    gap: 12px;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.trending-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.trending-item img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    flex-shrink: 0;
}

.trending-content h4 {
    font-size: 13px;
    font-weight: 500;
    line-height: 1.4;
}

.trending-content a {
    color: #212529;
    text-decoration: none;
    transition: color 0.3s ease;
}

.trending-content a:hover {
    color: #007bff;
}

/* Resource Items */
.resource-item {
    display: flex;
    gap: 12px;
    margin-bottom: 15px;
}

.resource-item:last-child {
    margin-bottom: 0;
}

.resource-item img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    flex-shrink: 0;
}

.resource-content h4 {
    font-size: 13px;
    font-weight: 500;
    line-height: 1.4;
    color: #212529;
}

/* Newsletter Signup */
.newsletter-signup {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.newsletter-signup h3 {
    color: white;
    margin-bottom: 10px;
}

.newsletter-signup p {
    font-size: 13px;
    margin-bottom: 15px;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.newsletter-form input {
    padding: 10px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
}

.newsletter-form button {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 10px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
}

.newsletter-form button:hover {
    background: rgba(255,255,255,0.3);
}

/* Social Connect */
.social-stat {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    color: #495057;
    text-decoration: none;
    font-size: 13px;
    transition: color 0.3s ease;
}

.social-stat:hover {
    color: #007bff;
}

.social-stat i {
    width: 20px;
    font-size: 14px;
}

/* Footer */
.footer {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 40px 0 20px;
    margin-top: 60px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #212529;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 8px;
}

.footer-section a {
    color: #6c757d;
    text-decoration: none;
    font-size: 13px;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #007bff;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
    font-size: 13px;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: #6c757d;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #007bff;
}

.copyright {
    color: #6c757d;
}

/* Chatbot Integration */
.chatbot-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    font-family: 'Inter', sans-serif;
}

.chatbot-toggle {
    width: 56px;
    height: 56px;
    background: #007bff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(0, 123, 255, 0.4);
    transition: all 0.3s ease;
    border: none;
    outline: none;
}

.chatbot-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(0, 123, 255, 0.6);
}

.chatbot-toggle i {
    color: white;
    font-size: 20px;
}

.chat-bubbles {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-width: 280px;
    margin-bottom: 10px;
}

.chat-bubble {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    max-width: 100%;
}

.chat-bubble.show {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.chat-bubble.hide {
    opacity: 0;
    transform: translateY(-10px) scale(0.9);
}

.bubble-content {
    display: flex;
    align-items: flex-end;
    gap: 8px;
}

.bubble-avatar {
    width: 28px;
    height: 28px;
    background: #007bff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.bubble-avatar i {
    color: white;
    font-size: 12px;
}

.bubble-text {
    background: white;
    padding: 10px 14px;
    border-radius: 18px;
    font-size: 13px;
    line-height: 1.4;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    max-width: 220px;
    word-wrap: break-word;
    border: 1px solid #e9ecef;
}

.user-bubble {
    align-self: flex-end;
}

.user-bubble .bubble-content {
    flex-direction: row-reverse;
}

.user-bubble .bubble-text {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.chat-input-bubble {
    display: flex;
    align-items: center;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 8px 12px;
    gap: 8px;
    max-width: 280px;
    opacity: 0;
    transform: translateY(20px) scale(0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

.chat-input-bubble.active {
    opacity: 1;
    transform: translateY(0) scale(1);
    pointer-events: all;
}

#chatbot-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 13px;
    padding: 8px 4px;
    color: #333;
    min-width: 180px;
}

#chatbot-input::placeholder {
    color: #999;
    font-size: 13px;
}

.chat-send-btn {
    width: 32px;
    height: 32px;
    background: #007bff;
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.chat-send-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
}

.chat-send-btn i {
    font-size: 12px;
}

.typing-dots {
    display: flex;
    gap: 3px;
    padding: 2px 0;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #007bff;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    30% {
        transform: translateY(-8px);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .banner-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .nav-desktop {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .main-nav-menu {
        flex-wrap: wrap;
    }
    
    .main-nav-menu .nav-link {
        padding: 12px 15px;
        font-size: 13px;
    }
    
    .main-content {
        flex-direction: column;
        padding: 20px;
        gap: 30px;
    }
    
    .sidebar {
        width: 100%;
    }
    
    .page-title h1 {
        font-size: 24px;
    }
    
    .main-article .article-title {
        font-size: 20px;
    }
    
    .secondary-article {
        flex-direction: column;
    }
    
    .secondary-article .article-image {
        width: 100%;
        height: 200px;
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .main-content {
        padding: 15px;
    }
    
    .main-nav-menu .nav-link {
        padding: 10px 12px;
        font-size: 12px;
    }
    
    .sidebar-section {
        padding: 15px;
    }
}
