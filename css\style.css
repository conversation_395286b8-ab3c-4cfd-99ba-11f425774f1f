/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --white: #ffffff;
    --black: #000000;
    
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    --transition: all 0.3s ease;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-size: var(--font-size-base);
    font-weight: 500;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    gap: 0.5rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    color: var(--white);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* Navigation */
.navbar {
    background-color: var(--white);
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
}

.nav-logo a {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-logo i {
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    font-weight: 500;
    color: var(--gray-700);
    padding: 0.5rem 0;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--primary-color);
}

/* Dropdown */
.nav-dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--white);
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius);
    padding: 0.5rem 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.nav-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--gray-700);
    font-weight: 400;
}

.dropdown-link:hover {
    background-color: var(--gray-100);
    color: var(--primary-color);
}

/* Mobile Navigation */
.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 0.25rem;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--gray-700);
    transition: var(--transition);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    padding: 8rem 0 4rem;
    margin-top: 4rem;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.hero-content {
    text-align: center;
    margin-bottom: 4rem;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.highlight {
    color: var(--accent-color);
}

.hero-description {
    font-size: var(--font-size-xl);
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    text-align: center;
}

.stat-item {
    padding: 1.5rem;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: var(--font-size-base);
    opacity: 0.9;
}

/* Market Overview */
.market-overview {
    padding: 4rem 0;
    background-color: var(--gray-100);
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--dark-color);
}

.market-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.market-card {
    background-color: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
}

.market-card h3 {
    margin-bottom: 1.5rem;
    color: var(--dark-color);
}

.market-data {
    min-height: 200px;
}

.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--gray-500);
}

/* Featured Articles */
.featured {
    padding: 4rem 0;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.article-card {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.article-link {
    text-decoration: none;
    color: inherit;
    display: block;
    height: 100%;
}

.featured-article {
    grid-column: 1 / -1;
}

.article-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.article-card:hover .article-image img {
    transform: scale(1.05);
}

.article-category {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background-color: var(--primary-color);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.article-content {
    padding: 1.5rem;
}

.article-title {
    margin-bottom: 0.75rem;
    color: var(--dark-color);
}

.article-excerpt {
    color: var(--gray-600);
    margin-bottom: 1rem;
}

.article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

/* Newsletter */
.newsletter {
    background-color: var(--dark-color);
    color: var(--white);
    padding: 4rem 0;
}

.newsletter-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.newsletter-title {
    margin-bottom: 1rem;
}

.newsletter-description {
    margin-bottom: 2rem;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    display: flex;
    gap: 1rem;
}

.form-group input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
}

.form-group input:focus {
    outline: 2px solid var(--primary-color);
}

.form-message {
    padding: 0.75rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    text-align: center;
}

.form-message.success {
    background-color: var(--success-color);
    color: var(--white);
}

.form-message.error {
    background-color: var(--danger-color);
    color: var(--white);
}

/* Footer */
.footer {
    background-color: var(--gray-900);
    color: var(--gray-300);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--white);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.footer-logo i {
    color: var(--primary-color);
}

.footer-description {
    margin-bottom: 1.5rem;
    opacity: 0.8;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--gray-800);
    color: var(--gray-300);
    border-radius: 50%;
    transition: var(--transition);
}

.social-link:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.footer-title {
    color: var(--white);
    margin-bottom: 1rem;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--gray-300);
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.contact-info i {
    color: var(--primary-color);
    width: 16px;
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: 1rem;
    text-align: center;
    opacity: 0.8;
}

/* Minimal Chat Bubble Interface */
.chatbot-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    font-family: 'Inter', sans-serif;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
}

/* Chat Bubbles Container */
.chat-bubbles {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-width: 280px;
    margin-bottom: 10px;
}

/* Individual Chat Bubble */
.chat-bubble {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    max-width: 100%;
}

.chat-bubble.show {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.chat-bubble.hide {
    opacity: 0;
    transform: translateY(-10px) scale(0.9);
}

.bubble-content {
    display: flex;
    align-items: flex-end;
    gap: 8px;
}

.bubble-avatar {
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.bubble-avatar i {
    color: white;
    font-size: 12px;
}

.bubble-text {
    background: white;
    padding: 10px 14px;
    border-radius: 18px;
    font-size: 13px;
    line-height: 1.4;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    max-width: 220px;
    word-wrap: break-word;
}

.bot-bubble .bubble-text {
    background: white;
    color: #333;
}

.user-bubble {
    align-self: flex-end;
}

.user-bubble .bubble-content {
    flex-direction: row-reverse;
}

.user-bubble .bubble-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* Typing Animation */
.typing-dots {
    display: flex;
    gap: 3px;
    padding: 2px 0;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #667eea;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    30% {
        transform: translateY(-8px);
        opacity: 1;
    }
}

/* Floating Chat Button */
.chatbot-toggle {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    border: none;
    outline: none;
}

.chatbot-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
}

.chatbot-toggle i {
    color: white;
    font-size: 20px;
    transition: transform 0.3s ease;
}

.chatbot-toggle:hover i {
    transform: rotate(10deg);
}

/* Minimal Input Bubble */
.chat-input-bubble {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 8px 12px;
    gap: 8px;
    max-width: 280px;
    opacity: 0;
    transform: translateY(20px) scale(0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

.chat-input-bubble.active {
    opacity: 1;
    transform: translateY(0) scale(1);
    pointer-events: all;
}

.chatbot-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chatbot-header-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.chatbot-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chatbot-avatar i {
    font-size: 18px;
}

.chatbot-title h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.chatbot-status {
    font-size: 12px;
    opacity: 0.8;
    display: flex;
    align-items: center;
    gap: 5px;
}

.chatbot-status::before {
    content: '';
    width: 8px;
    height: 8px;
    background: #2ed573;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.chatbot-close {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.chatbot-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.chatbot-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
    background: #f8f9fa;
}

.chatbot-messages::-webkit-scrollbar {
    width: 4px;
}

.chatbot-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chatbot-messages::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 2px;
}

.chatbot-message {
    display: flex;
    flex-direction: column;
    max-width: 80%;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.user-message {
    align-self: flex-end;
}

.bot-message {
    align-self: flex-start;
}

.message-content {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.bot-avatar {
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.bot-avatar i {
    color: white;
    font-size: 12px;
}

.message-text {
    background: white;
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.4;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    white-space: pre-line;
}

.user-message .message-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-left: auto;
}

.message-text a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.user-message .message-text a {
    color: rgba(255, 255, 255, 0.9);
}

.message-time {
    font-size: 11px;
    color: #999;
    margin-top: 5px;
    text-align: right;
}

.bot-message .message-time {
    text-align: left;
    margin-left: 40px;
}

.typing-indicator .typing-dots {
    display: flex;
    gap: 4px;
    padding: 12px 16px;
    background: white;
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: #667eea;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

.chatbot-quick-actions {
    padding: 15px 20px;
    background: white;
    border-top: 1px solid #eee;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quick-action-btn {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    padding: 8px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    color: #495057;
}

.quick-action-btn:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
    transform: translateY(-1px);
}

.quick-action-btn i {
    font-size: 10px;
}

.quick-action-text {
    display: inline;
}

/* Hide text on very small screens, show only icons */
@media (max-width: 380px) {
    .quick-action-text {
        display: none;
    }

    .quick-action-btn {
        min-width: 40px;
        justify-content: center;
    }
}

#chatbot-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 13px;
    padding: 8px 4px;
    color: #333;
    min-width: 180px;
}

#chatbot-input::placeholder {
    color: #999;
    font-size: 13px;
}

.chat-send-btn {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.chat-send-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.chat-send-btn:active {
    transform: scale(0.95);
}

.chat-send-btn i {
    font-size: 12px;
}

/* Minimal Chat Interface - Mobile Responsive */

/* Large Desktop (1200px+) */
@media (min-width: 1200px) {
    .chatbot-toggle {
        width: 60px;
        height: 60px;
    }

    .chatbot-toggle i {
        font-size: 22px;
    }

    .chat-bubbles {
        max-width: 320px;
    }

    .bubble-text {
        font-size: 14px;
        padding: 12px 16px;
        max-width: 260px;
    }

    .chat-input-bubble {
        max-width: 320px;
    }

    #chatbot-input {
        font-size: 14px;
        min-width: 220px;
    }
}

/* Standard Desktop (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .chatbot-window {
        width: 350px;
        height: 500px;
    }
}

/* Tablet Landscape (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .chatbot-window {
        width: 320px;
        height: 480px;
        bottom: 75px;
        right: 10px;
    }

    .chatbot-container {
        bottom: 15px;
        right: 15px;
    }

    .chatbot-toggle {
        width: 58px;
        height: 58px;
    }

    .chatbot-toggle i {
        font-size: 24px;
    }

    .chatbot-messages {
        padding: 18px;
        gap: 16px;
    }

    .quick-action-btn {
        font-size: 12px;
        padding: 7px 11px;
    }

    .chatbot-input-container {
        padding: 18px;
    }
}

/* Tablet Portrait (576px - 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .chatbot-window {
        width: 300px;
        height: 450px;
        bottom: 70px;
        right: 10px;
    }

    .chatbot-container {
        bottom: 15px;
        right: 15px;
    }

    .chatbot-toggle {
        width: 55px;
        height: 55px;
    }

    .chatbot-toggle i {
        font-size: 22px;
    }

    .chatbot-messages {
        padding: 16px;
        gap: 14px;
    }

    .message-text {
        font-size: 13px;
        padding: 11px 15px;
    }

    .quick-action-btn {
        font-size: 11px;
        padding: 6px 10px;
        gap: 4px;
    }

    .quick-action-btn i {
        font-size: 9px;
    }

    .chatbot-input-container {
        padding: 16px;
        gap: 8px;
    }

    #chatbot-input {
        padding: 11px 15px;
        font-size: 13px;
    }

    .chatbot-send-btn {
        width: 36px;
        height: 36px;
    }
}

/* Mobile Large (480px - 575px) */
@media (min-width: 480px) and (max-width: 575px) {
    .chatbot-container {
        bottom: 15px;
        right: 15px;
    }

    .chatbot-toggle {
        width: 50px;
        height: 50px;
    }

    .chatbot-toggle i {
        font-size: 18px;
    }

    .chat-bubbles {
        max-width: 240px;
    }

    .bubble-text {
        font-size: 12px;
        padding: 9px 12px;
        max-width: 180px;
    }

    .bubble-avatar {
        width: 24px;
        height: 24px;
    }

    .bubble-avatar i {
        font-size: 10px;
    }

    .chat-input-bubble {
        max-width: 240px;
        padding: 6px 10px;
    }

    #chatbot-input {
        font-size: 12px;
        min-width: 140px;
    }

    .chat-send-btn {
        width: 28px;
        height: 28px;
    }

    .chat-send-btn i {
        font-size: 10px;
    }
}

/* Mobile Small (320px - 479px) */
@media (max-width: 479px) {
    .chatbot-container {
        bottom: 10px;
        right: 10px;
    }

    .chatbot-toggle {
        width: 46px;
        height: 46px;
        box-shadow: 0 3px 15px rgba(102, 126, 234, 0.4);
    }

    .chatbot-toggle i {
        font-size: 16px;
    }

    .chat-bubbles {
        max-width: 200px;
    }

    .bubble-text {
        font-size: 11px;
        padding: 8px 10px;
        max-width: 150px;
        border-radius: 14px;
    }

    .bubble-avatar {
        width: 22px;
        height: 22px;
    }

    .bubble-avatar i {
        font-size: 9px;
    }

    .chat-input-bubble {
        max-width: 200px;
        padding: 5px 8px;
        border-radius: 20px;
    }

    #chatbot-input {
        font-size: 11px;
        min-width: 120px;
        padding: 6px 2px;
    }

    #chatbot-input::placeholder {
        font-size: 10px;
    }

    .chat-send-btn {
        width: 26px;
        height: 26px;
    }

    .chat-send-btn i {
        font-size: 9px;
    }

    .typing-dots span {
        width: 4px;
        height: 4px;
    }
}

/* Extra Small Mobile (max 320px) */
@media (max-width: 320px) {
    .chatbot-window {
        width: calc(100vw - 16px);
        height: 380px;
        right: 8px;
        left: 8px;
        bottom: 60px;
    }

    .chatbot-container {
        bottom: 8px;
        right: 12px;
    }

    .chatbot-toggle {
        width: 46px;
        height: 46px;
    }

    .chatbot-toggle i {
        font-size: 16px;
    }

    .chatbot-messages {
        padding: 10px;
        gap: 8px;
    }

    .message-text {
        font-size: 11px;
        padding: 8px 10px;
    }

    .quick-action-btn {
        font-size: 8px;
        padding: 3px 5px;
    }

    #chatbot-input {
        font-size: 11px;
        padding: 8px 10px;
    }

    .chatbot-send-btn {
        width: 30px;
        height: 30px;
    }
}

/* Landscape Orientation Adjustments */
@media (max-height: 500px) and (orientation: landscape) {
    .chatbot-window {
        height: calc(100vh - 80px);
        max-height: 350px;
    }

    .chatbot-messages {
        padding: 10px;
    }

    .quick-action-btn {
        padding: 4px 8px;
        font-size: 10px;
    }

    .chatbot-input-container {
        padding: 10px;
    }
}

/* High DPI / Retina Display Adjustments */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .chatbot-toggle {
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.5);
    }

    .chatbot-window {
        box-shadow: 0 12px 45px rgba(0, 0, 0, 0.18);
    }

    .message-text {
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
    }
}

/* Mobile App Specific Optimizations */

/* PWA/Mobile App Full-Screen Mode */
@media (display-mode: standalone) {
    .chatbot-container {
        bottom: 30px;
        right: 20px;
    }

    .chatbot-window {
        bottom: 90px;
        box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2);
    }
}

/* iOS Safari Specific */
@supports (-webkit-touch-callout: none) {
    .chatbot-window {
        -webkit-backdrop-filter: blur(10px);
        backdrop-filter: blur(10px);
    }

    .chatbot-messages {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }

    #chatbot-input {
        -webkit-appearance: none;
        border-radius: 25px;
    }
}

/* Android Specific */
@media (max-width: 768px) and (orientation: portrait) {
    .chatbot-window {
        width: calc(100vw - 20px);
        height: calc(100vh - 120px);
        max-height: 600px;
        bottom: 80px;
        right: 10px;
        left: 10px;
        border-radius: 20px 20px 0 0;
    }

    .chatbot-container.mobile-app-mode {
        bottom: 10px;
        right: 10px;
    }

    .chatbot-window.mobile-app-mode {
        width: 100vw;
        height: 100vh;
        bottom: 0;
        right: 0;
        left: 0;
        top: 0;
        border-radius: 0;
        max-height: none;
    }
}

/* Touch Device Optimizations */
@media (pointer: coarse) {
    .chatbot-toggle {
        width: 60px;
        height: 60px;
        touch-action: manipulation;
        -webkit-user-select: none;
        user-select: none;
    }

    .quick-action-btn {
        min-height: 44px;
        padding: 8px 12px;
        touch-action: manipulation;
        -webkit-user-select: none;
        user-select: none;
    }

    #chatbot-input {
        min-height: 44px;
        touch-action: manipulation;
        -webkit-user-select: text;
        user-select: text;
    }

    .chatbot-send-btn {
        min-width: 44px;
        min-height: 44px;
        touch-action: manipulation;
        -webkit-user-select: none;
        user-select: none;
    }

    .chatbot-close {
        min-width: 44px;
        min-height: 44px;
        touch-action: manipulation;
        -webkit-user-select: none;
        user-select: none;
    }

    .message-text {
        -webkit-user-select: text;
        user-select: text;
    }
}

/* Gesture Support */
.chatbot-window.swipe-down {
    transform: translateY(10px);
    transition: transform 0.2s ease;
}

.chatbot-window.swipe-up {
    transform: translateY(-10px);
    transition: transform 0.2s ease;
}

/* Haptic Feedback Simulation */
.chatbot-toggle.haptic-feedback {
    animation: hapticPulse 0.1s ease;
}

@keyframes hapticPulse {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

.quick-action-btn.haptic-feedback {
    animation: hapticTap 0.1s ease;
}

@keyframes hapticTap {
    0% { transform: scale(1); }
    50% { transform: scale(0.98); }
    100% { transform: scale(1); }
}

/* Mobile App Status Bar Safe Area */
@supports (padding-top: env(safe-area-inset-top)) {
    .chatbot-window.mobile-app-mode {
        padding-top: env(safe-area-inset-top);
        height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
    }

    .chatbot-container.mobile-app-mode {
        bottom: calc(10px + env(safe-area-inset-bottom));
    }
}

/* Dark Mode Support for Mobile Apps */
@media (prefers-color-scheme: dark) {
    .chatbot-window {
        background: #1a1a1a;
        color: #ffffff;
    }

    .chatbot-header {
        background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    }

    .chatbot-messages {
        background: #2d3748;
    }

    .message-text {
        background: #4a5568;
        color: #ffffff;
    }

    .user-message .message-text {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    #chatbot-input {
        background: #4a5568;
        color: #ffffff;
        border-color: #718096;
    }

    #chatbot-input::placeholder {
        color: #a0aec0;
    }

    .quick-action-btn {
        background: #4a5568;
        color: #ffffff;
        border-color: #718096;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .chatbot-window {
        transition: opacity 0.2s ease;
        transform: none;
    }

    .chatbot-toggle {
        transition: background 0.2s ease;
    }

    .typing-dots span {
        animation: none;
    }

    .chatbot-message {
        animation: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .chatbot-window {
        border: 2px solid #000000;
    }

    .chatbot-toggle {
        border: 2px solid #ffffff;
    }

    .message-text {
        border: 1px solid #000000;
    }

    #chatbot-input {
        border: 2px solid #000000;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 4rem;
        left: -100%;
        width: 100%;
        height: calc(100vh - 4rem);
        background-color: var(--white);
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        padding: 2rem;
        transition: var(--transition);
        box-shadow: var(--shadow-lg);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        background-color: var(--gray-100);
        margin-top: 0.5rem;
        border-radius: var(--border-radius);
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-description {
        font-size: var(--font-size-lg);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-stats {
        grid-template-columns: 1fr;
    }
    
    .articles-grid {
        grid-template-columns: 1fr;
    }
    
    .featured-article {
        grid-column: 1;
    }
    
    .form-group {
        flex-direction: column;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    padding: 6rem 0 3rem;
    margin-top: 4rem;
    text-align: center;
}

.page-title {
    font-size: var(--font-size-4xl);
    margin-bottom: 1rem;
}

.page-description {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Market Dashboard */
.market-dashboard {
    padding: 4rem 0;
    background-color: var(--gray-100);
}

.indices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.index-card {
    background-color: var(--white);
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    text-align: center;
}

.index-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.index-header h3 {
    margin: 0;
    color: var(--dark-color);
}

.index-symbol {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-weight: 500;
}

.index-price {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.index-change {
    font-weight: 500;
}

/* Stocks Section */
.stocks-section {
    margin-top: 3rem;
}

.stocks-section h3 {
    margin-bottom: 2rem;
    color: var(--dark-color);
}

.stocks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.stock-card {
    background-color: var(--white);
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.stock-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stock-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.stock-info h4 {
    margin: 0 0 0.25rem 0;
    color: var(--dark-color);
    font-size: var(--font-size-lg);
}

.stock-symbol {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-weight: 500;
}

.stock-logo {
    font-size: var(--font-size-xl);
    color: var(--primary-color);
}

.stock-price {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.stock-change {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.stock-volume {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

/* Market News */
.market-news {
    padding: 4rem 0;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.news-card {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.news-card.featured {
    grid-column: 1 / -1;
}

.news-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.news-card.featured .news-image {
    height: 300px;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}

.news-category {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background-color: var(--primary-color);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.news-content {
    padding: 1.5rem;
}

.news-title {
    margin-bottom: 0.75rem;
    color: var(--dark-color);
}

.news-excerpt {
    color: var(--gray-600);
    margin-bottom: 1rem;
}

.news-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.news-author {
    font-weight: 500;
}

/* Market Tools */
.market-tools {
    padding: 4rem 0;
    background-color: var(--gray-100);
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.tool-card {
    background-color: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.tool-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.tool-icon {
    font-size: var(--font-size-4xl);
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.tool-card h3 {
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.tool-card p {
    color: var(--gray-600);
    margin-bottom: 1.5rem;
}

@media (max-width: 480px) {
    .container {
        padding: 0 0.75rem;
    }

    .hero {
        padding: 6rem 0 3rem;
    }

    .hero-title {
        font-size: var(--font-size-2xl);
    }

    .section-title {
        font-size: var(--font-size-2xl);
    }

    .market-grid {
        grid-template-columns: 1fr;
    }

    .articles-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .article-card {
        margin: 0 0.5rem;
    }

    .page-title {
        font-size: var(--font-size-2xl);
    }

    .indices-grid,
    .stocks-grid,
    .news-grid,
    .tools-grid {
        grid-template-columns: 1fr;
    }

    .news-card.featured {
        grid-column: 1;
    }
}

/* Crypto Dashboard Styles */
.crypto-dashboard {
    padding: 4rem 0;
    background-color: var(--gray-100);
}

.market-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background-color: var(--white);
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    width: 50px;
    text-align: center;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

/* Crypto Section */
.crypto-section {
    margin-top: 3rem;
}

.crypto-section h3 {
    margin-bottom: 2rem;
    color: var(--dark-color);
}

.crypto-table {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.crypto-header {
    display: grid;
    grid-template-columns: 50px 2fr 1fr 1fr 1fr 1fr;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background-color: var(--gray-100);
    font-weight: 600;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

.crypto-row {
    display: grid;
    grid-template-columns: 50px 2fr 1fr 1fr 1fr 1fr;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    align-items: center;
    transition: var(--transition);
    cursor: pointer;
}

.crypto-row:hover {
    background-color: var(--gray-50);
}

.crypto-row:last-child {
    border-bottom: none;
}

.crypto-rank {
    font-weight: 600;
    color: var(--gray-600);
}

.crypto-name {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.crypto-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: var(--font-size-sm);
}

.crypto-info {
    display: flex;
    flex-direction: column;
}

.crypto-title {
    font-weight: 600;
    color: var(--dark-color);
}

.crypto-symbol {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.crypto-price {
    font-weight: 600;
    color: var(--dark-color);
}

.crypto-change {
    font-weight: 500;
}

.crypto-volume,
.crypto-market-cap {
    color: var(--gray-600);
}

/* Crypto News */
.crypto-news {
    padding: 4rem 0;
}

/* Crypto Tools */
.crypto-tools {
    padding: 4rem 0;
    background-color: var(--gray-100);
}

/* Responsive Crypto Table */
@media (max-width: 768px) {
    .crypto-header,
    .crypto-row {
        grid-template-columns: 30px 2fr 1fr 1fr;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
    }

    .crypto-volume,
    .crypto-market-cap {
        display: none;
    }

    .crypto-header .crypto-volume,
    .crypto-header .crypto-market-cap {
        display: none;
    }

    .market-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        width: auto;
    }
}

@media (max-width: 480px) {
    .crypto-header,
    .crypto-row {
        grid-template-columns: 25px 2fr 1fr;
        gap: 0.25rem;
        padding: 0.5rem 0.75rem;
    }

    .crypto-change {
        display: none;
    }

    .crypto-header .crypto-change {
        display: none;
    }

    .crypto-name {
        gap: 0.5rem;
    }

    .crypto-icon {
        width: 24px;
        height: 24px;
        font-size: var(--font-size-xs);
    }
}

/* Cybersecurity Pages Styles */
.breach-stats {
    padding: 4rem 0;
    background-color: var(--gray-100);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

/* Recent Breaches */
.recent-breaches {
    padding: 4rem 0;
}

.breach-timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.breach-timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--primary-color);
}

.breach-item {
    display: flex;
    margin-bottom: 2rem;
    position: relative;
}

.breach-item::before {
    content: '';
    position: absolute;
    left: 24px;
    top: 1rem;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: var(--primary-color);
    border: 3px solid var(--white);
    box-shadow: 0 0 0 3px var(--primary-color);
}

.breach-date {
    min-width: 100px;
    font-weight: 600;
    color: var(--gray-600);
    padding-right: 2rem;
}

.breach-content {
    flex: 1;
    background-color: var(--white);
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    margin-left: 2rem;
}

.breach-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.breach-company {
    margin: 0;
    color: var(--dark-color);
}

.breach-severity {
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.breach-severity.critical {
    background-color: var(--danger-color);
    color: var(--white);
}

.breach-severity.high {
    background-color: #ff6b35;
    color: var(--white);
}

.breach-severity.medium {
    background-color: var(--warning-color);
    color: var(--white);
}

.breach-severity.low {
    background-color: var(--success-color);
    color: var(--white);
}

.breach-description {
    color: var(--gray-700);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.breach-details {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.breach-records,
.breach-type,
.breach-status {
    padding: 0.25rem 0.5rem;
    background-color: var(--gray-100);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    color: var(--gray-700);
}

/* Protection Tips */
.protection-tips {
    padding: 4rem 0;
    background-color: var(--gray-100);
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.tip-card {
    background-color: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.tip-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.tip-icon {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.tip-card h3 {
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.tip-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Breach Articles */
.breach-articles {
    padding: 4rem 0;
}

/* Attack Types */
.attack-types {
    padding: 4rem 0;
    background-color: var(--gray-100);
}

.attack-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.attack-card {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.attack-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.attack-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--danger-color) 0%, #ff6b35 100%);
    color: var(--white);
}

.attack-icon {
    font-size: var(--font-size-2xl);
    margin-bottom: 0.5rem;
}

.attack-title {
    margin: 0;
    font-size: var(--font-size-xl);
}

.attack-content {
    padding: 1.5rem;
}

.attack-description {
    color: var(--gray-700);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.attack-examples {
    margin-bottom: 1rem;
}

.attack-examples h4 {
    font-size: var(--font-size-base);
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.attack-examples ul {
    list-style: none;
    padding: 0;
}

.attack-examples li {
    padding: 0.25rem 0;
    color: var(--gray-600);
    position: relative;
    padding-left: 1rem;
}

.attack-examples li::before {
    content: '•';
    color: var(--primary-color);
    position: absolute;
    left: 0;
}

.attack-prevention {
    background-color: var(--gray-100);
    padding: 1rem;
    border-radius: var(--border-radius);
}

.attack-prevention h4 {
    font-size: var(--font-size-base);
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.attack-prevention p {
    margin: 0;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

/* Vulnerability Styles */
.vulnerability-levels {
    padding: 4rem 0;
    background-color: var(--gray-100);
}

.vulnerability-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.vulnerability-card {
    background-color: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    border-left: 4px solid var(--primary-color);
}

.vulnerability-card.critical {
    border-left-color: var(--danger-color);
}

.vulnerability-card.high {
    border-left-color: #ff6b35;
}

.vulnerability-card.medium {
    border-left-color: var(--warning-color);
}

.vulnerability-card.low {
    border-left-color: var(--success-color);
}

.vulnerability-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.vulnerability-title {
    margin: 0;
    color: var(--dark-color);
}

.vulnerability-score {
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--white);
}

.vulnerability-score.critical {
    background-color: var(--danger-color);
}

.vulnerability-score.high {
    background-color: #ff6b35;
}

.vulnerability-score.medium {
    background-color: var(--warning-color);
}

.vulnerability-score.low {
    background-color: var(--success-color);
}

.vulnerability-description {
    color: var(--gray-700);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.vulnerability-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.vulnerability-detail {
    display: flex;
    flex-direction: column;
}

.vulnerability-detail-label {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    margin-bottom: 0.25rem;
}

.vulnerability-detail-value {
    font-weight: 600;
    color: var(--dark-color);
}

/* Responsive Design for Cybersecurity Pages */
@media (max-width: 768px) {
    .breach-timeline::before {
        left: 15px;
    }

    .breach-item::before {
        left: 9px;
    }

    .breach-item {
        flex-direction: column;
    }

    .breach-date {
        min-width: auto;
        padding-right: 0;
        margin-bottom: 0.5rem;
        margin-left: 2rem;
    }

    .breach-content {
        margin-left: 2rem;
    }

    .breach-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .breach-details {
        flex-direction: column;
        gap: 0.5rem;
    }

    .tips-grid,
    .attack-grid,
    .vulnerability-grid {
        grid-template-columns: 1fr;
    }

    .vulnerability-details {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        width: auto;
        margin-bottom: 0.5rem;
    }
}

/* About Page Styles */
.about-mission {
    padding: 4rem 0;
}

.mission-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.mission-text h2 {
    margin-bottom: 2rem;
    color: var(--dark-color);
}

.mission-text p {
    font-size: var(--font-size-lg);
    line-height: 1.8;
    color: var(--gray-700);
    margin-bottom: 1.5rem;
}

.mission-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

/* Team Section */
.team-section {
    padding: 4rem 0;
    background-color: var(--gray-100);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.team-member {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.member-photo {
    width: 120px;
    height: 120px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid var(--primary-color);
}

.member-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.member-name {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.member-role {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.member-bio {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.member-social {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.member-social .social-link {
    width: 40px;
    height: 40px;
    background-color: var(--gray-100);
    color: var(--gray-600);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.member-social .social-link:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* Values Section */
.values-section {
    padding: 4rem 0;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.value-card {
    text-align: center;
    padding: 2rem;
}

.value-icon {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.value-card h3 {
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.value-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* History Section */
.history-section {
    padding: 4rem 0;
    background-color: var(--gray-100);
}

.timeline {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--primary-color);
}

.timeline-item {
    display: flex;
    margin-bottom: 3rem;
    position: relative;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 24px;
    top: 0.5rem;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: var(--primary-color);
    border: 3px solid var(--white);
    box-shadow: 0 0 0 3px var(--primary-color);
}

.timeline-year {
    min-width: 80px;
    font-weight: 700;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    padding-right: 2rem;
}

.timeline-content {
    flex: 1;
    background-color: var(--white);
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    margin-left: 2rem;
}

.timeline-content h3 {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.timeline-content p {
    color: var(--gray-700);
    line-height: 1.6;
    margin: 0;
}

/* Contact Page Styles */
.contact-section {
    padding: 4rem 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
}

.contact-form-container h2,
.contact-info-container h2 {
    margin-bottom: 2rem;
    color: var(--dark-color);
}

/* Contact Form */
.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--gray-700);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.checkbox-group {
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--gray-300);
    border-radius: 3px;
    position: relative;
    transition: var(--transition);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: var(--white);
    font-size: 12px;
    font-weight: bold;
}

/* Contact Info */
.contact-info-grid {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 2rem;
}

.contact-info-item {
    display: flex;
    gap: 1rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.contact-details h3 {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.contact-details p {
    color: var(--gray-700);
    margin-bottom: 0.25rem;
}

.contact-note {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-style: italic;
}

.contact-social {
    padding-top: 2rem;
    border-top: 1px solid var(--gray-200);
}

.contact-social h3 {
    margin-bottom: 1rem;
    color: var(--dark-color);
}

/* FAQ Section */
.faq-section {
    padding: 4rem 0;
    background-color: var(--gray-100);
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.faq-item {
    background-color: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
}

.faq-question {
    margin-bottom: 1rem;
    color: var(--dark-color);
    font-size: var(--font-size-lg);
}

.faq-answer {
    color: var(--gray-700);
    line-height: 1.6;
    margin: 0;
}

/* Responsive Design for About and Contact Pages */
@media (max-width: 768px) {
    .mission-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .mission-stats {
        grid-template-columns: 1fr;
    }

    .team-grid {
        grid-template-columns: 1fr;
    }

    .values-grid {
        grid-template-columns: 1fr;
    }

    .timeline::before {
        left: 15px;
    }

    .timeline-item::before {
        left: 9px;
    }

    .timeline-item {
        flex-direction: column;
    }

    .timeline-year {
        min-width: auto;
        padding-right: 0;
        margin-bottom: 0.5rem;
        margin-left: 2rem;
    }

    .timeline-content {
        margin-left: 2rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .member-photo {
        width: 100px;
        height: 100px;
    }

    .contact-info-item {
        flex-direction: column;
        text-align: center;
    }

    .contact-icon {
        align-self: center;
    }
}

/* Article Page Styles */
.article-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    padding: 6rem 0 3rem;
    margin-top: 4rem;
}

.article-breadcrumb {
    margin-bottom: 1rem;
    opacity: 0.9;
}

.article-breadcrumb a {
    color: var(--white);
    text-decoration: none;
    opacity: 0.8;
}

.article-breadcrumb a:hover {
    opacity: 1;
}

.article-category-badge {
    display: inline-block;
    background-color: var(--accent-color);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin-bottom: 1rem;
}

.article-title {
    font-size: var(--font-size-4xl);
    margin-bottom: 2rem;
    line-height: 1.2;
}

.article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid var(--white);
}

.author-details {
    display: flex;
    flex-direction: column;
}

.author-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.author-title {
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

.article-info {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.article-info span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

/* Article Content */
.article-content {
    padding: 4rem 0;
}

.article-body {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.article-text {
    max-width: none;
}

.article-image {
    margin-bottom: 2rem;
}

.article-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
}

.image-caption {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-style: italic;
    text-align: center;
    margin-top: 0.5rem;
}

.lead {
    font-size: var(--font-size-xl);
    line-height: 1.7;
    color: var(--gray-700);
    margin-bottom: 2rem;
    font-weight: 400;
}

.article-text h2 {
    color: var(--dark-color);
    margin-top: 2.5rem;
    margin-bottom: 1rem;
    font-size: var(--font-size-2xl);
}

.article-text h3 {
    color: var(--dark-color);
    margin-top: 2rem;
    margin-bottom: 0.75rem;
    font-size: var(--font-size-xl);
}

.article-text p {
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: var(--gray-700);
}

.article-text ul,
.article-text ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.article-text li {
    line-height: 1.7;
    margin-bottom: 0.5rem;
    color: var(--gray-700);
}

/* Article Components */
.info-box,
.warning-box {
    background-color: var(--gray-100);
    border-left: 4px solid var(--primary-color);
    padding: 1.5rem;
    margin: 2rem 0;
    border-radius: var(--border-radius);
}

.warning-box {
    background-color: #fef3cd;
    border-left-color: var(--warning-color);
}

.info-box-header,
.warning-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.info-box-header h3,
.warning-header h3 {
    margin: 0;
    color: var(--dark-color);
}

.info-box-header i,
.warning-header i {
    color: var(--primary-color);
}

.warning-header i {
    color: var(--warning-color);
}

.timeline-box {
    margin: 2rem 0;
}

.timeline-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    align-items: flex-start;
}

.timeline-date {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: var(--font-size-sm);
    min-width: 80px;
    text-align: center;
}

.timeline-content {
    flex: 1;
    background-color: var(--gray-100);
    padding: 1rem;
    border-radius: var(--border-radius);
}

.timeline-content h4 {
    margin: 0 0 0.5rem 0;
    color: var(--dark-color);
}

.timeline-content p {
    margin: 0;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

.action-steps {
    margin: 2rem 0;
}

.step {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    align-items: flex-start;
}

.step-number {
    background-color: var(--primary-color);
    color: var(--white);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-content h4 {
    margin: 0 0 0.5rem 0;
    color: var(--dark-color);
}

.step-content p {
    margin: 0;
    color: var(--gray-700);
}

blockquote {
    border-left: 4px solid var(--primary-color);
    padding: 1.5rem;
    margin: 2rem 0;
    background-color: var(--gray-50);
    font-style: italic;
    font-size: var(--font-size-lg);
}

blockquote p {
    margin-bottom: 0.5rem;
    color: var(--gray-800);
}

blockquote cite {
    font-size: var(--font-size-base);
    color: var(--gray-600);
    font-weight: 600;
}

/* Company Performance */
.company-performance {
    margin: 2rem 0;
}

.company-item {
    background-color: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.company-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.company-header h4 {
    margin: 0;
    color: var(--dark-color);
}

.performance-badge {
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.performance-badge.positive {
    background-color: var(--success-color);
    color: var(--white);
}

.performance-badge.negative {
    background-color: var(--danger-color);
    color: var(--white);
}

.company-item p {
    margin: 0;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

/* Investment Strategies */
.investment-strategies {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.strategy-item {
    background-color: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    text-align: center;
}

.strategy-icon {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.strategy-content h4 {
    margin-bottom: 0.75rem;
    color: var(--dark-color);
}

.strategy-content p {
    margin: 0;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

/* Technical Analysis */
.technical-analysis {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.analysis-item {
    background-color: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--border-radius);
}

.analysis-item h4 {
    margin-bottom: 0.75rem;
    color: var(--dark-color);
}

.analysis-item p {
    margin: 0;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
    line-height: 1.6;
}

/* Sentiment Indicators */
.sentiment-indicators {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.sentiment-item {
    background-color: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    text-align: center;
}

.sentiment-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: 0.5rem;
}

.sentiment-value {
    font-size: var(--font-size-xl);
    font-weight: 700;
}

.sentiment-value.greed {
    color: var(--warning-color);
}

.sentiment-value.positive {
    color: var(--success-color);
}

.sentiment-value.high {
    color: var(--primary-color);
}

/* Altcoin Performance */
.altcoin-performance {
    margin: 2rem 0;
}

.coin-item {
    background-color: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.coin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.coin-name {
    font-weight: 600;
    color: var(--dark-color);
}

.coin-change {
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.coin-change.positive {
    color: var(--success-color);
}

.coin-change.negative {
    color: var(--danger-color);
}

.coin-item p {
    margin: 0;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

/* Expert Opinions */
.expert-opinions {
    margin: 2rem 0;
}

.opinion-item {
    background-color: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.expert-info {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.opinion-item p {
    margin: 0;
    color: var(--gray-700);
    font-style: italic;
}

/* Article Tags */
.article-tags {
    margin: 3rem 0 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--gray-200);
}

.article-tags h4 {
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.tag {
    display: inline-block;
    background-color: var(--gray-200);
    color: var(--gray-700);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.tag:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* Share Buttons */
.article-share {
    margin: 2rem 0;
    padding-top: 2rem;
    border-top: 1px solid var(--gray-200);
}

.article-share h4 {
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.share-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.share-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: var(--transition);
}

.share-btn.twitter {
    background-color: #1da1f2;
    color: var(--white);
}

.share-btn.linkedin {
    background-color: #0077b5;
    color: var(--white);
}

.share-btn.facebook {
    background-color: #1877f2;
    color: var(--white);
}

.share-btn.email {
    background-color: var(--gray-600);
    color: var(--white);
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* Article Sidebar */
.article-sidebar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.sidebar-widget {
    background-color: var(--white);
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
}

.sidebar-widget h3 {
    margin-bottom: 1rem;
    color: var(--dark-color);
    font-size: var(--font-size-lg);
}

/* Related Articles */
.related-articles {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.related-article {
    display: flex;
    gap: 1rem;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-decoration: none;
    color: inherit;
}

.related-article:hover {
    background-color: var(--gray-100);
}

.related-article img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--border-radius);
    flex-shrink: 0;
}

.related-content {
    flex: 1;
}

.related-content h4 {
    margin: 0 0 0.5rem 0;
    font-size: var(--font-size-base);
    color: var(--dark-color);
    line-height: 1.4;
}

.related-date {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

/* Market Widget */
.market-widget,
.crypto-widget {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.market-item,
.crypto-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background-color: var(--gray-100);
    border-radius: var(--border-radius);
}

.symbol,
.crypto-symbol {
    font-weight: 600;
    color: var(--dark-color);
}

.price,
.crypto-price {
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.price.positive,
.crypto-price.positive {
    color: var(--success-color);
}

.price.negative,
.crypto-price.negative {
    color: var(--danger-color);
}

/* Sidebar Newsletter */
.sidebar-newsletter {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.sidebar-newsletter input {
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
}

.sidebar-newsletter input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.sidebar-newsletter .btn {
    padding: 0.75rem;
    font-size: var(--font-size-sm);
}

/* Responsive Design for Articles */
@media (max-width: 768px) {
    .article-title {
        font-size: var(--font-size-2xl);
    }

    .article-meta {
        flex-direction: column;
        align-items: flex-start;
    }

    .article-body {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .article-sidebar {
        order: -1;
    }

    .investment-strategies,
    .technical-analysis,
    .sentiment-indicators {
        grid-template-columns: 1fr;
    }

    .share-buttons {
        justify-content: center;
    }

    .related-article {
        flex-direction: column;
        text-align: center;
    }

    .related-article img {
        width: 100%;
        height: 120px;
    }
}

@media (max-width: 480px) {
    .article-header {
        padding: 5rem 0 2rem;
    }

    .article-info {
        flex-direction: column;
        gap: 0.5rem;
    }

    .author-info {
        flex-direction: column;
        text-align: center;
    }

    .share-buttons {
        flex-direction: column;
    }

    .share-btn {
        justify-content: center;
    }
}
