/* TheHackerNews Exact Style Recreation */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Top Header - Blue Bar */
.top-header {
    background: #3f51b5;
    color: white;
    padding: 8px 0;
    font-size: 13px;
}

.top-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tagline {
    font-weight: 500;
}

.social-follow {
    display: flex;
    align-items: center;
    gap: 10px;
}

.social-follow span {
    font-size: 12px;
    opacity: 0.9;
}

.social-icon {
    color: white;
    text-decoration: none;
    font-size: 14px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.social-icon:hover {
    opacity: 1;
}

/* Main Header */
.main-header {
    background: white;
    padding: 15px 0;
    border-bottom: 1px solid #e0e0e0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-size: 32px;
    font-weight: 800;
    color: #3f51b5;
    text-decoration: none;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.subscribe-btn {
    background: #ffc107;
    color: #333;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.subscribe-btn:hover {
    background: #ffb300;
}

.search-btn, .mobile-menu-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    padding: 8px;
}

.mobile-menu-btn {
    display: none;
}

/* Navigation */
.main-navigation {
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    border-right: 1px solid #e0e0e0;
}

.nav-menu li:last-child {
    border-right: none;
}

.nav-link {
    display: block;
    padding: 15px 20px;
    color: #666;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    background: #3f51b5;
    color: white;
}

/* Main Content Layout */
.main-content {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
    gap: 30px;
}

.content-area {
    flex: 2;
}

.sidebar {
    flex: 1;
    max-width: 300px;
}

/* Featured Article */
.featured-article {
    margin-bottom: 20px;
    display: flex;
    gap: 15px;
    align-items: flex-start;
}

.article-image-large {
    width: 120px;
    height: 80px;
    flex-shrink: 0;
    border-radius: 4px;
    overflow: hidden;
}

.article-image-large .placeholder-img {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    font-weight: 600;
}

/* Purple Ad Banner */
.ad-banner-purple {
    background: linear-gradient(135deg, #8e24aa, #5e35b1);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    color: white;
}

.ad-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.ad-logo {
    background: white;
    color: #8e24aa;
    padding: 8px 12px;
    border-radius: 4px;
    font-weight: 700;
    font-size: 14px;
}

.ad-text h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
    line-height: 1.2;
}

.ad-text p {
    font-size: 14px;
    margin-bottom: 15px;
    opacity: 0.9;
}

.ad-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
}

.ad-btn:hover {
    background: rgba(255,255,255,0.3);
}

/* Articles */
.article {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    padding-bottom: 25px;
    border-bottom: 1px solid #f0f0f0;
}

.article:last-child {
    border-bottom: none;
}

.article-image {
    flex-shrink: 0;
    width: 180px;
    height: 120px;
    border-radius: 4px;
    overflow: hidden;
}

.article-image img,
.article-image .placeholder-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.placeholder-img {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    font-weight: 600;
    position: relative;
}

.placeholder-img.large {
    font-size: 32px;
}

.placeholder-img::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.2);
}

.placeholder-img::after {
    content: attr(data-text);
    position: relative;
    z-index: 2;
}

.article-content {
    flex: 1;
}

.article-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 8px;
    font-size: 12px;
    color: #666;
}

.article-author {
    color: #666;
    font-weight: 500;
}

.article-date {
    color: #666;
}

.article-category {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.article-title {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 8px;
}

.article-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.article-title a:hover {
    color: #3f51b5;
}

.article-excerpt {
    color: #666;
    font-size: 13px;
    line-height: 1.5;
}

/* Sidebar */
.sidebar-section {
    margin-bottom: 25px;
}

.sidebar-section:first-child {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.sidebar-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
    line-height: 1.3;
}

.sidebar-item {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.sidebar-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.sidebar-item-image {
    width: 50px;
    height: 50px;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
}

.sidebar-item-image .placeholder-img {
    font-size: 14px;
}

.sidebar-item-content {
    flex: 1;
}

.sidebar-item-content h4 {
    font-size: 13px;
    font-weight: 500;
    line-height: 1.3;
    margin-bottom: 5px;
}

.sidebar-item-content a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.sidebar-item-content a:hover {
    color: #3f51b5;
}

.sidebar-item-meta {
    font-size: 11px;
    color: #999;
}

/* Newsletter */
.newsletter-section {
    background: linear-gradient(135deg, #3f51b5, #303f9f);
    color: white;
}

.newsletter-section .sidebar-title {
    color: white;
    border-color: rgba(255,255,255,0.3);
}

.newsletter-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.newsletter-form input {
    padding: 10px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
}

.newsletter-form button {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 10px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
}

.newsletter-form button:hover {
    background: rgba(255,255,255,0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .top-header-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .mobile-menu-btn {
        display: block;
    }
    
    .nav-menu {
        flex-wrap: wrap;
    }
    
    .nav-link {
        padding: 12px 15px;
        font-size: 13px;
    }
    
    .main-content {
        flex-direction: column;
        padding: 20px;
        gap: 30px;
    }
    
    .sidebar {
        max-width: 100%;
    }
    
    .article {
        flex-direction: column;
    }
    
    .article-image {
        width: 100%;
        height: 200px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .main-content {
        padding: 15px;
    }
    
    .logo h1 {
        font-size: 24px;
    }
    
    .subscribe-btn {
        padding: 8px 15px;
        font-size: 12px;
    }
}

/* Next Page Navigation */
.next-page-nav {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 40px;
}

.next-page-link {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.next-page-link:hover {
    color: #3f51b5;
    background-color: #f8f9fa;
}

/* Expert Insights Section */
.expert-insights-section {
    margin-bottom: 50px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.section-title {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    margin: 0;
}

.section-nav {
    display: flex;
    gap: 20px;
}

.section-nav-link {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    padding-bottom: 2px;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.section-nav-link.active,
.section-nav-link:hover {
    color: #3f51b5;
    border-bottom-color: #3f51b5;
}

.expert-insights-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
}

.expert-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    position: relative;
    transition: all 0.3s ease;
}

.expert-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.expert-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    border-radius: 8px 8px 0 0;
}

.expert-header {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 15px;
}

.expert-info {
    flex: 1;
}

.expert-name {
    font-size: 16px;
    font-weight: 700;
    color: #333;
    margin: 0 0 5px 0;
}

.expert-title {
    font-size: 13px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

.expert-avatar {
    flex-shrink: 0;
}

.expert-img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.company-logos {
    display: flex;
    flex-direction: column;
    gap: 5px;
    align-items: center;
}

.company-logo-single {
    display: flex;
    align-items: center;
}

.company-logo {
    width: 40px;
    height: 40px;
    border-radius: 4px;
}

.expert-article-title {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
    margin: 0 0 15px 0;
}

.expert-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.expert-date {
    font-size: 12px;
    color: #666;
    display: flex;
    align-items: center;
    gap: 5px;
}

.read-link {
    color: #3f51b5;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
}

.read-link:hover {
    text-decoration: underline;
}

/* Cybersecurity Resources Detailed Section */
.cybersecurity-resources-detailed {
    margin-bottom: 50px;
}

.resources-detailed-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-top: 30px;
}

.resource-detailed-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.resource-detailed-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.resource-detailed-image {
    width: 100%;
    height: 175px;
    overflow: hidden;
}

.resource-detailed-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.resource-detailed-card:hover .resource-detailed-img {
    transform: scale(1.02);
}

.resource-detailed-content {
    padding: 20px;
}

.resource-detailed-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
    margin: 0 0 12px 0;
}

.resource-detailed-description {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    margin: 0;
}

/* Newsletter Signup Section */
.newsletter-signup-section {
    background: linear-gradient(135deg, #4c63d2 0%, #5a67d8 100%);
    padding: 60px 0;
    margin: 50px 0;
    border-radius: 12px;
    text-align: center;
}

.newsletter-content {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 20px;
}

.newsletter-title {
    font-size: 32px;
    font-weight: 700;
    color: white;
    margin: 0 0 15px 0;
    line-height: 1.2;
}

.newsletter-description {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.5;
    margin: 0 0 40px 0;
}

.newsletter-form {
    max-width: 500px;
    margin: 0 auto;
}

.newsletter-input-group {
    display: flex;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.newsletter-input {
    flex: 1;
    padding: 18px 20px;
    border: none;
    font-size: 16px;
    color: #333;
    background: transparent;
    outline: none;
}

.newsletter-input::placeholder {
    color: #999;
}

.newsletter-submit-btn {
    background: #4c63d2;
    border: none;
    padding: 18px 25px;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.newsletter-submit-btn:hover {
    background: #3f51b5;
    transform: translateX(2px);
}

/* Responsive Design for Expert Insights and Resources */
@media (max-width: 1200px) {
    .expert-insights-grid,
    .resources-detailed-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 900px) {
    .expert-insights-grid,
    .resources-detailed-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .newsletter-title {
        font-size: 28px;
    }

    .newsletter-description {
        font-size: 15px;
    }
}

@media (max-width: 600px) {
    .expert-insights-grid,
    .resources-detailed-grid {
        grid-template-columns: 1fr;
    }

    .expert-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .company-logos {
        flex-direction: row;
    }

    .newsletter-signup-section {
        padding: 40px 0;
        margin: 30px 0;
    }

    .newsletter-title {
        font-size: 24px;
    }

    .newsletter-input-group {
        flex-direction: column;
    }

    .newsletter-submit-btn {
        border-radius: 0 0 8px 8px;
    }
}
