/* TheHackerNews Exact Style Recreation */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Top Header - Blue Bar */
.top-header {
    background: #3f51b5;
    color: white;
    padding: 8px 0;
    font-size: 13px;
}

.top-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tagline {
    font-weight: 500;
}

.social-follow {
    display: flex;
    align-items: center;
    gap: 10px;
}

.social-follow span {
    font-size: 12px;
    opacity: 0.9;
}

.social-icon {
    color: white;
    text-decoration: none;
    font-size: 14px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.social-icon:hover {
    opacity: 1;
}

/* Main Header */
.main-header {
    background: white;
    padding: 15px 0;
    border-bottom: 1px solid #e0e0e0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-size: 32px;
    font-weight: 800;
    color: #3f51b5;
    text-decoration: none;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.subscribe-btn {
    background: #ffc107;
    color: #333;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.subscribe-btn:hover {
    background: #ffb300;
}

.search-btn, .mobile-menu-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    padding: 8px;
}

.mobile-menu-btn {
    display: none;
}

/* Navigation */
.main-navigation {
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    border-right: 1px solid #e0e0e0;
}

.nav-menu li:last-child {
    border-right: none;
}

.nav-link {
    display: block;
    padding: 15px 20px;
    color: #666;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    background: #3f51b5;
    color: white;
}

/* Main Content Layout */
.main-content {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
    gap: 30px;
}

.content-area {
    flex: 2;
}

.sidebar {
    flex: 1;
    max-width: 300px;
}

/* Featured Article */
.featured-article {
    margin-bottom: 20px;
    display: flex;
    gap: 15px;
    align-items: flex-start;
}

.article-image-large {
    width: 120px;
    height: 80px;
    flex-shrink: 0;
    border-radius: 4px;
    overflow: hidden;
}

.article-image-large .placeholder-img {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    font-weight: 600;
}

/* Purple Ad Banner */
.ad-banner-purple {
    background: linear-gradient(135deg, #8e24aa, #5e35b1);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    color: white;
}

.ad-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.ad-logo {
    background: white;
    color: #8e24aa;
    padding: 8px 12px;
    border-radius: 4px;
    font-weight: 700;
    font-size: 14px;
}

.ad-text h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
    line-height: 1.2;
}

.ad-text p {
    font-size: 14px;
    margin-bottom: 15px;
    opacity: 0.9;
}

.ad-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
}

.ad-btn:hover {
    background: rgba(255,255,255,0.3);
}

/* Articles */
.article {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    padding-bottom: 25px;
    border-bottom: 1px solid #f0f0f0;
}

.article:last-child {
    border-bottom: none;
}

.article-image {
    flex-shrink: 0;
    width: 180px;
    height: 120px;
    border-radius: 4px;
    overflow: hidden;
}

.article-image img,
.article-image .placeholder-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.placeholder-img {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    font-weight: 600;
    position: relative;
}

.placeholder-img.large {
    font-size: 32px;
}

.placeholder-img::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.2);
}

.placeholder-img::after {
    content: attr(data-text);
    position: relative;
    z-index: 2;
}

.article-content {
    flex: 1;
}

.article-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 8px;
    font-size: 12px;
    color: #666;
}

.article-author {
    color: #666;
    font-weight: 500;
}

.article-date {
    color: #666;
}

.article-category {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.article-title {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 8px;
}

.article-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.article-title a:hover {
    color: #3f51b5;
}

.article-excerpt {
    color: #666;
    font-size: 13px;
    line-height: 1.5;
}

/* Sidebar */
.sidebar-section {
    margin-bottom: 25px;
}

.sidebar-section:first-child {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.sidebar-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
    line-height: 1.3;
}

.sidebar-item {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.sidebar-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.sidebar-item-image {
    width: 50px;
    height: 50px;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
}

.sidebar-item-image .placeholder-img {
    font-size: 14px;
}

.sidebar-item-content {
    flex: 1;
}

.sidebar-item-content h4 {
    font-size: 13px;
    font-weight: 500;
    line-height: 1.3;
    margin-bottom: 5px;
}

.sidebar-item-content a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.sidebar-item-content a:hover {
    color: #3f51b5;
}

.sidebar-item-meta {
    font-size: 11px;
    color: #999;
}

/* Newsletter */
.newsletter-section {
    background: linear-gradient(135deg, #3f51b5, #303f9f);
    color: white;
}

.newsletter-section .sidebar-title {
    color: white;
    border-color: rgba(255,255,255,0.3);
}

.newsletter-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.newsletter-form input {
    padding: 10px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
}

.newsletter-form button {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 10px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
}

.newsletter-form button:hover {
    background: rgba(255,255,255,0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .top-header-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .mobile-menu-btn {
        display: block;
    }
    
    .nav-menu {
        flex-wrap: wrap;
    }
    
    .nav-link {
        padding: 12px 15px;
        font-size: 13px;
    }
    
    .main-content {
        flex-direction: column;
        padding: 20px;
        gap: 30px;
    }
    
    .sidebar {
        max-width: 100%;
    }
    
    .article {
        flex-direction: column;
    }
    
    .article-image {
        width: 100%;
        height: 200px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .main-content {
        padding: 15px;
    }
    
    .logo h1 {
        font-size: 24px;
    }
    
    .subscribe-btn {
        padding: 8px 15px;
        font-size: 12px;
    }
}
