/* TheHackerNews Real Layout Recreation */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Main Header */
.main-header {
    background: #3f51b5;
    color: white;
    padding: 15px 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    flex: 1;
}

.tagline {
    font-size: 13px;
    font-weight: 400;
    color: rgba(255,255,255,0.9);
}

.logo {
    flex: 1;
    text-align: center;
}

.logo h1 {
    font-size: 32px;
    font-weight: 700;
    color: white;
    margin: 0;
    letter-spacing: -0.5px;
}

.header-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
}

.social-follow {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.social-follow span {
    color: rgba(255,255,255,0.9);
}

.social-icon {
    color: white;
    text-decoration: none;
    font-size: 14px;
    transition: opacity 0.3s ease;
}

.social-icon:hover {
    opacity: 0.8;
}

.subscribe-btn {
    background: #ffc107;
    color: #333;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.subscribe-btn:hover {
    background: #ffb300;
}

/* Navigation */
.main-navigation {
    background: white;
    padding: 12px 0;
    border-bottom: 1px solid #e0e0e0;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    padding: 5px 0;
    transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: #3f51b5;
}

.nav-actions {
    display: flex;
    gap: 10px;
}

.search-btn, .mobile-menu-btn {
    background: none;
    border: none;
    font-size: 16px;
    color: #666;
    cursor: pointer;
    padding: 8px;
}

.search-btn:hover, .mobile-menu-btn:hover {
    color: #3f51b5;
}

/* Main Content */
.main-content {
    padding: 20px 0;
}

.content-wrapper {
    display: flex;
    gap: 30px;
}

.content-area {
    flex: 2;
}

/* ThreatLabs Banner */
.top-banner-ad {
    margin-bottom: 20px;
}

.threatlab-banner {
    background: linear-gradient(135deg, #e53e3e, #c53030);
    border-radius: 8px;
    overflow: hidden;
}

.banner-content {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    gap: 20px;
}

.banner-logo {
    flex-shrink: 0;
}

.threatlab-logo {
    color: white;
    font-weight: 600;
    font-size: 14px;
}

.banner-text {
    flex: 1;
    color: white;
}

.banner-text h3 {
    font-size: 18px;
    font-weight: 700;
    margin: 0 0 2px 0;
}

.banner-text p {
    font-size: 14px;
    margin: 0 0 2px 0;
    opacity: 0.9;
}

.banner-text small {
    font-size: 12px;
    opacity: 0.8;
}

.banner-cta {
    flex-shrink: 0;
}

.get-report-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.get-report-btn:hover {
    background: rgba(255,255,255,0.3);
}

/* Featured Article Horizontal */
.featured-article-horizontal {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    padding-bottom: 30px;
    border-bottom: 1px solid #f0f0f0;
}

.featured-article-horizontal .featured-image {
    flex-shrink: 0;
    width: 293px;
    height: 196px;
    border-radius: 8px;
    overflow: hidden;
}

.featured-article-horizontal .featured-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-article-horizontal .featured-content {
    flex: 1;
    padding-top: 10px;
}

.featured-title {
    font-size: 24px;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 10px;
}

.featured-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.featured-title a:hover {
    color: #3f51b5;
}

.featured-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 13px;
    color: #666;
}

.featured-date {
    display: flex;
    align-items: center;
    gap: 5px;
}

.featured-category {
    background: #e3f2fd;
    color: #1976d2;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 500;
    font-size: 12px;
}

.featured-excerpt {
    font-size: 15px;
    line-height: 1.6;
    color: #555;
}

/* Article Card */
.article-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.article-sponsor {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.sponsor-label {
    background: #3f51b5;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
}

.article-card .article-category {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
    font-size: 11px;
}

.article-card .article-title {
    font-size: 18px;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 10px;
}

.article-card .article-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.article-card .article-title a:hover {
    color: #3f51b5;
}

.article-card .article-excerpt {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

/* Articles */
.article {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    padding-bottom: 25px;
    border-bottom: 1px solid #f0f0f0;
}

.article:last-child {
    border-bottom: none;
}

.article-image {
    flex-shrink: 0;
    width: 270px;
    height: 180px;
    border-radius: 6px;
    overflow: hidden;
}

.article-image img,
.article-image .placeholder-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.article-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.placeholder-img {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    font-weight: 600;
    position: relative;
}

.placeholder-img::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.2);
}

.placeholder-img::after {
    content: attr(data-text);
    position: relative;
    z-index: 2;
}

.article-content {
    flex: 1;
}

.article-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 8px;
    font-size: 12px;
    color: #666;
}

.article-date {
    color: #666;
}

.article-category {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.article-title {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 8px;
}

.article-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.article-title a:hover {
    color: #3f51b5;
}

.article-excerpt {
    color: #666;
    font-size: 13px;
    line-height: 1.5;
}

/* Sidebar */
.sidebar {
    flex: 1;
    max-width: 300px;
}

/* CISO Board Report Ad */
.ciso-board-ad {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    color: white;
}

.ciso-board-ad .ad-header h3 {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 5px;
    color: white;
}

.ciso-board-ad .ad-header p {
    font-size: 12px;
    opacity: 0.9;
    margin-bottom: 15px;
    line-height: 1.4;
}

.ad-visual {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

.chart-placeholder {
    position: relative;
    width: 80px;
    height: 60px;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-icon {
    font-size: 24px;
}

.chart-bars {
    position: absolute;
    bottom: 10px;
    right: 10px;
    display: flex;
    gap: 2px;
}

.bar {
    width: 3px;
    background: rgba(255,255,255,0.6);
    border-radius: 1px;
}

.bar1 { height: 8px; }
.bar2 { height: 12px; }
.bar3 { height: 6px; }

.ad-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ad-logo {
    font-size: 14px;
    font-weight: 700;
    color: white;
}

.download-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.download-btn:hover {
    background: rgba(255,255,255,0.3);
}

/* ZScaler Ad */
.zscaler-ad {
    background: linear-gradient(135deg, #6b46c1, #ec4899);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    height: 120px;
    position: relative;
    overflow: hidden;
}

.zscaler-content {
    position: relative;
    z-index: 2;
}

.zscaler-logo {
    color: white;
    font-size: 16px;
    font-weight: 700;
}

.gradient-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 70% 30%, rgba(255,255,255,0.1) 0%, transparent 50%);
}

.sidebar-item {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.sidebar-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.sidebar-item-image {
    width: 50px;
    height: 50px;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
}

.sidebar-item-image .placeholder-img {
    font-size: 14px;
}

.sidebar-item-content {
    flex: 1;
}

.sidebar-item-content h4 {
    font-size: 13px;
    font-weight: 500;
    line-height: 1.3;
    margin-bottom: 5px;
}

.sidebar-item-content a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.sidebar-item-content a:hover {
    color: #3f51b5;
}

.sidebar-item-meta {
    font-size: 11px;
    color: #999;
}

/* Trending News Section */
.trending-section {
    margin-bottom: 30px;
}

.trending-section .sidebar-title {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3f51b5;
}

.trending-section .sidebar-item {
    display: flex;
    gap: 12px;
    margin-bottom: 18px;
    padding-bottom: 18px;
    border-bottom: 1px solid #eee;
}

.trending-section .sidebar-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.trending-section .sidebar-item-image {
    width: 60px;
    height: 60px;
    border-radius: 6px;
    overflow: hidden;
    flex-shrink: 0;
}

.trending-section .sidebar-item-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.trending-section .sidebar-item-content h4 {
    font-size: 14px;
    font-weight: 600;
    line-height: 1.4;
    margin: 0;
}

.trending-section .sidebar-item-content a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.trending-section .sidebar-item-content a:hover {
    color: #3f51b5;
}

/* Text Only Article */
.text-only-article {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 15px;
    margin-bottom: 25px;
}

.text-only-article .article-content {
    padding: 0;
}

.text-only-article .article-excerpt {
    font-size: 15px;
    line-height: 1.6;
    color: #555;
    margin: 0;
}

/* SentinelOne Sponsored Article */
.sponsored-article {
    margin-bottom: 30px;
}

.sentinelone-ad {
    width: 270px;
    height: 180px;
    background: linear-gradient(135deg, #6b46c1 0%, #8b5cf6 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.sentinelone-content {
    text-align: center;
    color: white;
    z-index: 2;
    position: relative;
}

.sentinelone-logo {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    opacity: 0.9;
}

.sentinelone-text h3 {
    font-size: 24px;
    font-weight: 700;
    line-height: 1.2;
    margin: 0;
}

.sentinelone-text p {
    font-size: 12px;
    margin: 8px 0 15px 0;
    opacity: 0.9;
    line-height: 1.3;
}

.read-report-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.read-report-btn:hover {
    background: rgba(255,255,255,0.3);
}

/* Weekly Recap Title Styling */
.trending-section .sidebar-title {
    font-size: 16px;
    font-weight: 700;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3f51b5;
    line-height: 1.4;
}

/* Newsletter Section */
.newsletter-section {
    background: linear-gradient(135deg, #3f51b5, #1976d2);
    color: white;
    padding: 20px;
    border-radius: 8px;
}

.newsletter-form {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.newsletter-form input {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
}

.newsletter-form button {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 10px 15px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
}

.newsletter-form button:hover {
    background: rgba(255,255,255,0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .top-header-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
    }

    .nav-menu {
        flex-direction: column;
        gap: 15px;
    }

    .secondary-menu {
        flex-wrap: wrap;
        gap: 15px;
    }

    .content-wrapper {
        flex-direction: column;
        gap: 20px;
    }

    .sidebar {
        max-width: none;
    }

    .article {
        flex-direction: column;
    }

    .article-image {
        width: 100%;
        height: 200px;
    }

    .featured-title {
        font-size: 24px;
    }

    .newsletter-form {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .featured-title {
        font-size: 20px;
    }

    .article-title {
        font-size: 14px;
    }

    .sidebar-item {
        flex-direction: column;
        text-align: center;
    }

    .sidebar-item-image {
        width: 100%;
        height: 120px;
    }
}
