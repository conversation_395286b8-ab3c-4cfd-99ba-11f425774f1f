<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyber Attacks - CyberFinance Hub</title>
    <meta name="description" content="Learn about different types of cyber attacks, their methods, and how to defend against them.">
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html">
                    <i class="fas fa-shield-alt"></i>
                    CyberFinance Hub
                </a>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="stock-market.html" class="nav-link">Stock Market</a>
                <a href="cryptocurrency.html" class="nav-link">Cryptocurrency</a>
                <div class="nav-dropdown">
                    <a href="#" class="nav-link dropdown-toggle active">Cybersecurity <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="data-breaches.html" class="dropdown-link">Data Breaches</a>
                        <a href="cyber-attacks.html" class="dropdown-link active">Cyber Attacks</a>
                        <a href="vulnerabilities.html" class="dropdown-link">Vulnerabilities</a>
                    </div>
                </div>
                <a href="about.html" class="nav-link">About</a>
                <a href="contact.html" class="nav-link">Contact</a>
            </div>
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1 class="page-title">Cyber Attacks</h1>
                <p class="page-description">Understanding different types of cyber attacks, their methods, and effective defense strategies.</p>
            </div>
        </div>
    </section>

    <!-- Attack Statistics -->
    <section class="breach-stats">
        <div class="container">
            <h2 class="section-title">2024 Cyber Attack Statistics</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-virus"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">4.2M</div>
                        <div class="stat-label">Malware Attacks</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">3.4B</div>
                        <div class="stat-label">Phishing Emails</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-lock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">623</div>
                        <div class="stat-label">Ransomware Incidents</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">156K</div>
                        <div class="stat-label">DDoS Attacks</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Attack Types -->
    <section class="attack-types">
        <div class="container">
            <h2 class="section-title">Common Attack Types</h2>
            <div class="attack-grid">
                <div class="attack-card">
                    <div class="attack-header">
                        <div class="attack-icon">
                            <i class="fas fa-envelope-open-text"></i>
                        </div>
                        <h3 class="attack-title">Phishing</h3>
                    </div>
                    <div class="attack-content">
                        <p class="attack-description">Fraudulent attempts to obtain sensitive information by disguising as trustworthy entities in electronic communications.</p>
                        <div class="attack-examples">
                            <h4>Common Examples:</h4>
                            <ul>
                                <li>Fake banking emails</li>
                                <li>Malicious links in messages</li>
                                <li>Spoofed websites</li>
                                <li>Social media scams</li>
                            </ul>
                        </div>
                        <div class="attack-prevention">
                            <h4>Prevention:</h4>
                            <p>Verify sender identity, check URLs carefully, use email filters, and educate users about suspicious signs.</p>
                        </div>
                    </div>
                </div>

                <div class="attack-card">
                    <div class="attack-header">
                        <div class="attack-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <h3 class="attack-title">Ransomware</h3>
                    </div>
                    <div class="attack-content">
                        <p class="attack-description">Malicious software that encrypts files and demands payment for decryption keys, often targeting critical systems.</p>
                        <div class="attack-examples">
                            <h4>Common Examples:</h4>
                            <ul>
                                <li>File encryption attacks</li>
                                <li>System lockouts</li>
                                <li>Double extortion schemes</li>
                                <li>Supply chain attacks</li>
                            </ul>
                        </div>
                        <div class="attack-prevention">
                            <h4>Prevention:</h4>
                            <p>Regular backups, patch management, network segmentation, and employee training on suspicious attachments.</p>
                        </div>
                    </div>
                </div>

                <div class="attack-card">
                    <div class="attack-header">
                        <div class="attack-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <h3 class="attack-title">DDoS Attacks</h3>
                    </div>
                    <div class="attack-content">
                        <p class="attack-description">Distributed Denial of Service attacks overwhelm systems with traffic to make services unavailable to legitimate users.</p>
                        <div class="attack-examples">
                            <h4>Common Examples:</h4>
                            <ul>
                                <li>Volumetric attacks</li>
                                <li>Protocol attacks</li>
                                <li>Application layer attacks</li>
                                <li>Botnet-driven floods</li>
                            </ul>
                        </div>
                        <div class="attack-prevention">
                            <h4>Prevention:</h4>
                            <p>Use CDN services, implement rate limiting, deploy DDoS protection services, and monitor traffic patterns.</p>
                        </div>
                    </div>
                </div>

                <div class="attack-card">
                    <div class="attack-header">
                        <div class="attack-icon">
                            <i class="fas fa-user-secret"></i>
                        </div>
                        <h3 class="attack-title">Social Engineering</h3>
                    </div>
                    <div class="attack-content">
                        <p class="attack-description">Psychological manipulation techniques used to trick people into divulging confidential information or performing actions.</p>
                        <div class="attack-examples">
                            <h4>Common Examples:</h4>
                            <ul>
                                <li>Pretexting calls</li>
                                <li>Baiting with USB drives</li>
                                <li>Tailgating into buildings</li>
                                <li>Impersonation attacks</li>
                            </ul>
                        </div>
                        <div class="attack-prevention">
                            <h4>Prevention:</h4>
                            <p>Security awareness training, verification procedures, access controls, and incident reporting systems.</p>
                        </div>
                    </div>
                </div>

                <div class="attack-card">
                    <div class="attack-header">
                        <div class="attack-icon">
                            <i class="fas fa-bug"></i>
                        </div>
                        <h3 class="attack-title">Malware</h3>
                    </div>
                    <div class="attack-content">
                        <p class="attack-description">Malicious software designed to damage, disrupt, or gain unauthorized access to computer systems and networks.</p>
                        <div class="attack-examples">
                            <h4>Common Examples:</h4>
                            <ul>
                                <li>Trojans and viruses</li>
                                <li>Spyware and keyloggers</li>
                                <li>Rootkits and bootkits</li>
                                <li>Cryptocurrency miners</li>
                            </ul>
                        </div>
                        <div class="attack-prevention">
                            <h4>Prevention:</h4>
                            <p>Antivirus software, regular updates, safe browsing habits, and application whitelisting.</p>
                        </div>
                    </div>
                </div>

                <div class="attack-card">
                    <div class="attack-header">
                        <div class="attack-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h3 class="attack-title">SQL Injection</h3>
                    </div>
                    <div class="attack-content">
                        <p class="attack-description">Code injection technique that exploits vulnerabilities in database-driven applications to access or manipulate data.</p>
                        <div class="attack-examples">
                            <h4>Common Examples:</h4>
                            <ul>
                                <li>Login bypass attacks</li>
                                <li>Data extraction queries</li>
                                <li>Database modification</li>
                                <li>Blind SQL injection</li>
                            </ul>
                        </div>
                        <div class="attack-prevention">
                            <h4>Prevention:</h4>
                            <p>Parameterized queries, input validation, least privilege access, and regular security testing.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Recent Attacks -->
    <section class="recent-breaches">
        <div class="container">
            <h2 class="section-title">Recent Notable Attacks</h2>
            <div class="breach-timeline">
                <div class="breach-item">
                    <div class="breach-date">Dec 2024</div>
                    <div class="breach-content">
                        <div class="breach-header">
                            <h3 class="breach-company">Global Manufacturing Corp</h3>
                            <div class="breach-severity critical">Ransomware</div>
                        </div>
                        <p class="breach-description">Sophisticated ransomware attack encrypted critical production systems across 15 facilities worldwide, demanding $10M ransom. Operations halted for 72 hours before partial recovery.</p>
                        <div class="breach-details">
                            <span class="breach-records">15 Facilities</span>
                            <span class="breach-type">Ransomware</span>
                            <span class="breach-status">Recovering</span>
                        </div>
                    </div>
                </div>

                <div class="breach-item">
                    <div class="breach-date">Nov 2024</div>
                    <div class="breach-content">
                        <div class="breach-header">
                            <h3 class="breach-company">City Government Network</h3>
                            <div class="breach-severity high">DDoS</div>
                        </div>
                        <p class="breach-description">Coordinated DDoS attack targeted municipal services including emergency systems, lasting 18 hours and affecting 500K residents' access to online services.</p>
                        <div class="breach-details">
                            <span class="breach-records">500K Users</span>
                            <span class="breach-type">DDoS</span>
                            <span class="breach-status">Resolved</span>
                        </div>
                    </div>
                </div>

                <div class="breach-item">
                    <div class="breach-date">Oct 2024</div>
                    <div class="breach-content">
                        <div class="breach-header">
                            <h3 class="breach-company">Financial Services Inc</h3>
                            <div class="breach-severity critical">Phishing</div>
                        </div>
                        <p class="breach-description">Large-scale spear phishing campaign targeted executives and IT staff, resulting in credential theft and unauthorized access to customer financial data.</p>
                        <div class="breach-details">
                            <span class="breach-records">2.1M Customers</span>
                            <span class="breach-type">Phishing</span>
                            <span class="breach-status">Contained</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Defense Strategies -->
    <section class="protection-tips">
        <div class="container">
            <h2 class="section-title">Defense Strategies</h2>
            <div class="tips-grid">
                <div class="tip-card">
                    <div class="tip-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Multi-Layer Security</h3>
                    <p>Implement defense in depth with multiple security layers including firewalls, intrusion detection, and endpoint protection.</p>
                </div>

                <div class="tip-card">
                    <div class="tip-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3>Security Training</h3>
                    <p>Regular employee training on recognizing threats, safe practices, and incident reporting procedures.</p>
                </div>

                <div class="tip-card">
                    <div class="tip-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <h3>Regular Updates</h3>
                    <p>Keep all systems, software, and security tools updated with the latest patches and threat definitions.</p>
                </div>

                <div class="tip-card">
                    <div class="tip-icon">
                        <i class="fas fa-backup"></i>
                    </div>
                    <h3>Backup Strategy</h3>
                    <p>Implement comprehensive backup solutions with offline storage and regular recovery testing.</p>
                </div>

                <div class="tip-card">
                    <div class="tip-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3>Continuous Monitoring</h3>
                    <p>Deploy 24/7 security monitoring and incident response capabilities to detect and respond to threats quickly.</p>
                </div>

                <div class="tip-card">
                    <div class="tip-icon">
                        <i class="fas fa-users-cog"></i>
                    </div>
                    <h3>Access Control</h3>
                    <p>Implement principle of least privilege and multi-factor authentication for all critical systems.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Subscription -->
    <section class="newsletter">
        <div class="container">
            <div class="newsletter-content">
                <h2 class="newsletter-title">Stay Secure</h2>
                <p class="newsletter-description">Get the latest cyber attack alerts and defense strategies delivered to your inbox.</p>
                <form class="newsletter-form" id="newsletter-form">
                    <div class="form-group">
                        <input type="email" id="email" name="email" placeholder="Enter your email address" required>
                        <button type="submit" class="btn btn-primary">Subscribe</button>
                    </div>
                    <div class="form-message" id="form-message"></div>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-shield-alt"></i>
                        CyberFinance Hub
                    </div>
                    <p class="footer-description">Your trusted source for cybersecurity insights and financial market analysis.</p>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4 class="footer-title">Categories</h4>
                    <ul class="footer-links">
                        <li><a href="data-breaches.html">Data Breaches</a></li>
                        <li><a href="cyber-attacks.html">Cyber Attacks</a></li>
                        <li><a href="vulnerabilities.html">Vulnerabilities</a></li>
                        <li><a href="stock-market.html">Stock Market</a></li>
                        <li><a href="cryptocurrency.html">Cryptocurrency</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 class="footer-title">Company</h4>
                    <ul class="footer-links">
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 class="footer-title">Contact Info</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-phone"></i> +92 (*************</p>
                        <p><i class="fas fa-map-marker-alt"></i> Lahore, Pakistan</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 CyberFinance Hub. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>
