// AI Chatbot Assistant for CyberFinance Hub
class CyberFinanceChatbot {
    constructor() {
        this.isOpen = false;
        this.messages = [];
        this.isTyping = false;
        this.isMobile = window.innerWidth <= 768;
        this.isSmallMobile = window.innerWidth <= 480;
        this.isMobileApp = this.detectMobileApp();
        this.touchStartY = 0;
        this.touchEndY = 0;
        this.isScrolling = false;
        this.init();
        this.handleResize();
        this.initMobileAppFeatures();
    }

    init() {
        this.createChatbotHTML();
        this.bindEvents();
        this.addWelcomeMessage();
    }

    createChatbotHTML() {
        const chatbotHTML = `
            <div id="chatbot-container" class="chatbot-container">
                <!-- Chat Bubbles -->
                <div id="chat-bubbles" class="chat-bubbles">
                    <!-- Messages will appear here -->
                </div>

                <!-- Floating Chat <PERSON>ton -->
                <div id="chatbot-toggle" class="chatbot-toggle">
                    <i class="fas fa-robot"></i>
                </div>

                <!-- Minimal Input (appears when clicked) -->
                <div id="chat-input-bubble" class="chat-input-bubble">
                    <input type="text" id="chatbot-input" placeholder="Ask me anything..." maxlength="200">
                    <button id="chatbot-send" class="chat-send-btn">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', chatbotHTML);
    }

    bindEvents() {
        const toggle = document.getElementById('chatbot-toggle');
        const input = document.getElementById('chatbot-input');
        const send = document.getElementById('chatbot-send');

        toggle.addEventListener('click', () => this.toggleChat());
        send.addEventListener('click', () => this.sendMessage());
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendMessage();
        });

        // Auto-hide input after inactivity
        input.addEventListener('blur', () => {
            setTimeout(() => {
                if (!input.value.trim()) {
                    this.hideInput();
                }
            }, 3000);
        });

        // Handle window resize
        window.addEventListener('resize', () => this.handleResize());

        // Handle orientation change on mobile
        window.addEventListener('orientationchange', () => {
            setTimeout(() => this.handleResize(), 100);
        });

        // Click outside to hide input
        document.addEventListener('click', (e) => {
            if (!e.target.closest('#chatbot-container')) {
                this.hideInput();
            }
        });
    }

    toggleChat() {
        this.isOpen = !this.isOpen;
        const inputBubble = document.getElementById('chat-input-bubble');
        const toggle = document.getElementById('chatbot-toggle');

        // Haptic feedback for mobile apps
        if (this.isMobileApp) {
            this.triggerHapticFeedback('light');
        }

        if (this.isOpen) {
            this.showInput();
        } else {
            this.hideInput();
        }
    }

    showInput() {
        const inputBubble = document.getElementById('chat-input-bubble');
        const input = document.getElementById('chatbot-input');

        inputBubble.classList.add('active');
        this.isOpen = true;

        // Focus input after animation
        setTimeout(() => {
            input.focus();
        }, 200);
    }

    hideInput() {
        const inputBubble = document.getElementById('chat-input-bubble');
        const input = document.getElementById('chatbot-input');

        inputBubble.classList.remove('active');
        this.isOpen = false;
        input.blur();
    }

    addWelcomeMessage() {
        // Show a simple welcome bubble
        setTimeout(() => {
            this.showChatBubble("👋 Hi! Ask me about cybersecurity or finance", true);
        }, 1000);
    }

    showChatBubble(text, isBot = false, duration = 5000) {
        const bubblesContainer = document.getElementById('chat-bubbles');
        const bubble = document.createElement('div');
        bubble.className = `chat-bubble ${isBot ? 'bot-bubble' : 'user-bubble'}`;

        bubble.innerHTML = `
            <div class="bubble-content">
                ${isBot ? '<div class="bubble-avatar"><i class="fas fa-robot"></i></div>' : ''}
                <div class="bubble-text">${text}</div>
            </div>
        `;

        bubblesContainer.appendChild(bubble);

        // Animate in
        setTimeout(() => {
            bubble.classList.add('show');
        }, 100);

        // Auto-hide after duration
        setTimeout(() => {
            bubble.classList.add('hide');
            setTimeout(() => {
                if (bubble.parentNode) {
                    bubble.parentNode.removeChild(bubble);
                }
            }, 300);
        }, duration);

        // Limit number of bubbles
        const bubbles = bubblesContainer.querySelectorAll('.chat-bubble');
        if (bubbles.length > 3) {
            const oldestBubble = bubbles[0];
            oldestBubble.classList.add('hide');
            setTimeout(() => {
                if (oldestBubble.parentNode) {
                    oldestBubble.parentNode.removeChild(oldestBubble);
                }
            }, 300);
        }
    }

    sendMessage() {
        const input = document.getElementById('chatbot-input');
        const text = input.value.trim();

        if (!text) return;

        // Haptic feedback for mobile apps
        if (this.isMobileApp) {
            this.triggerHapticFeedback('light');
        }

        // Show user message bubble
        this.showChatBubble(text, false, 3000);

        // Clear input and hide it
        input.value = '';
        this.hideInput();

        // Show typing indicator
        this.showTypingBubble();

        // Generate AI response
        setTimeout(() => {
            this.hideTypingBubble();
            const response = this.generateResponse(text);
            this.showChatBubble(response.text, true, 8000);
        }, 1500);
    }

    showTypingBubble() {
        const bubblesContainer = document.getElementById('chat-bubbles');
        const typingBubble = document.createElement('div');
        typingBubble.className = 'chat-bubble bot-bubble typing-bubble';
        typingBubble.id = 'typing-bubble';

        typingBubble.innerHTML = `
            <div class="bubble-content">
                <div class="bubble-avatar"><i class="fas fa-robot"></i></div>
                <div class="bubble-text">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        `;

        bubblesContainer.appendChild(typingBubble);

        setTimeout(() => {
            typingBubble.classList.add('show');
        }, 100);
    }

    hideTypingBubble() {
        const typingBubble = document.getElementById('typing-bubble');
        if (typingBubble) {
            typingBubble.classList.add('hide');
            setTimeout(() => {
                if (typingBubble.parentNode) {
                    typingBubble.parentNode.removeChild(typingBubble);
                }
            }, 300);
        }
    }

    showTypingIndicator() {
        if (this.isTyping) return;
        
        this.isTyping = true;
        const messagesContainer = document.getElementById('chatbot-messages');
        const typingDiv = document.createElement('div');
        typingDiv.className = 'chatbot-message bot-message typing-indicator';
        typingDiv.id = 'typing-indicator';
        
        typingDiv.innerHTML = `
            <div class="message-content">
                <div class="bot-avatar"><i class="fas fa-robot"></i></div>
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
        
        messagesContainer.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
        this.isTyping = false;
    }

    generateResponse(userInput) {
        const input = userInput.toLowerCase();
        let response = "";

        // Cybersecurity responses
        if (input.includes('data breach') || input.includes('breach')) {
            response = "🔒 Data breaches are serious security incidents. Here's what you should do:\n\n• Change passwords immediately\n• Monitor your accounts closely\n• Enable two-factor authentication\n• Check our <a href='articles/anatomy-modern-data-breach-2024.html'>data breach guide</a> for detailed steps\n\nNeed specific help with a breach situation?";
        }
        else if (input.includes('password') || input.includes('authentication')) {
            response = "🔐 Strong passwords are your first line of defense:\n\n• Use 12+ characters with mixed case, numbers, symbols\n• Enable two-factor authentication (2FA)\n• Use a password manager\n• Never reuse passwords\n\nWould you like tips on choosing a password manager?";
        }
        else if (input.includes('phishing') || input.includes('email security')) {
            response = "📧 Phishing attacks are common. Watch for:\n\n• Urgent requests for personal info\n• Suspicious sender addresses\n• Unexpected attachments or links\n• Grammar/spelling errors\n\nAlways verify requests through official channels. Need help identifying a suspicious email?";
        }
        else if (input.includes('ransomware') || input.includes('malware')) {
            response = "🦠 Ransomware protection tips:\n\n• Keep software updated\n• Regular backups (offline storage)\n• Email security training\n• Network segmentation\n\nIf infected, don't pay ransom. Contact cybersecurity professionals immediately.";
        }
        else if (input.includes('vpn') || input.includes('privacy')) {
            response = "🌐 VPNs enhance online privacy:\n\n• Encrypts internet traffic\n• Hides IP address\n• Protects on public Wi-Fi\n• Choose reputable providers\n\nRecommended for banking, shopping, and sensitive activities.";
        }
        
        // Financial responses
        else if (input.includes('bitcoin') || input.includes('btc')) {
            response = "₿ Bitcoin insights:\n\n• Currently trading around $45,250\n• Institutional adoption growing\n• Regulatory clarity improving\n• Check our <a href='articles/bitcoin-market-analysis.html'>Bitcoin analysis</a>\n\nRemember: Crypto is volatile. Only invest what you can afford to lose.";
        }
        else if (input.includes('ethereum') || input.includes('eth')) {
            response = "⟠ Ethereum updates:\n\n• Proof-of-stake transition successful\n• 32M+ ETH staked\n• Strong DeFi ecosystem\n• Read our <a href='articles/ethereum-staking-milestone.html'>staking guide</a>\n\nInterested in staking or DeFi opportunities?";
        }
        else if (input.includes('stock') || input.includes('market') || input.includes('invest')) {
            response = "📈 Stock market guidance:\n\n• Diversify your portfolio\n• Consider dollar-cost averaging\n• Research before investing\n• Check our <a href='stock-market.html'>market analysis</a>\n\nWhat specific sectors interest you? Tech, energy, or banking?";
        }
        else if (input.includes('ai stocks') || input.includes('artificial intelligence')) {
            response = "🤖 AI stocks are hot:\n\n• NVIDIA leading with +187% gains\n• Microsoft integrating AI across products\n• $184B global AI market\n• Read our <a href='articles/ai-revolution-tech-stocks.html'>AI stock analysis</a>\n\nConsider both opportunities and risks in this rapidly evolving sector.";
        }
        else if (input.includes('defi') || input.includes('decentralized finance')) {
            response = "🏦 DeFi ecosystem update:\n\n• $103B+ total value locked\n• Lido Finance leading protocols\n• Growing institutional interest\n• Check our <a href='articles/defi-tvl-100b-milestone.html'>DeFi analysis</a>\n\nInterested in yield farming or liquidity providing?";
        }
        else if (input.includes('nft') || input.includes('non-fungible')) {
            response = "🎨 NFT market recovery:\n\n• Focus shifting to utility\n• Gaming integration growing\n• Floor prices stabilizing\n• Read our <a href='articles/nft-market-recovery.html'>NFT market report</a>\n\nLook for projects with real-world applications and strong communities.";
        }
        
        // Healthcare security
        else if (input.includes('healthcare') || input.includes('medical') || input.includes('hipaa')) {
            response = "🏥 Healthcare cybersecurity is critical:\n\n• 88M patients affected in 2024\n• HIPAA compliance required\n• Ransomware targeting hospitals\n• Read our <a href='articles/healthcare-data-breaches-concern.html'>healthcare security guide</a>\n\nAre you a healthcare professional or patient concerned about data security?";
        }
        
        // Financial protection
        else if (input.includes('financial security') || input.includes('bank') || input.includes('credit card')) {
            response = "💳 Financial data protection:\n\n• Monitor accounts regularly\n• Use strong authentication\n• Avoid public Wi-Fi for banking\n• Check our <a href='articles/protecting-financial-data-digital-age.html'>financial security guide</a>\n\nNeed help with specific banking security concerns?";
        }
        
        // General greetings
        else if (input.includes('hello') || input.includes('hi') || input.includes('hey')) {
            response = "👋 Hello! I'm here to help with cybersecurity and financial questions. I can assist with:\n\n• Data breach response\n• Password security\n• Investment guidance\n• Cryptocurrency insights\n• Financial protection\n\nWhat would you like to know about?";
        }
        else if (input.includes('help') || input.includes('what can you do')) {
            response = "🤖 I can help you with:\n\n**Cybersecurity:**\n• Data breach response\n• Password & authentication\n• Phishing protection\n• Ransomware prevention\n\n**Finance:**\n• Stock market insights\n• Cryptocurrency analysis\n• Investment strategies\n• Financial security\n\nJust ask me anything!";
        }
        
        // Default response
        else {
            const responses = [
                "I understand you're asking about cybersecurity or finance. Could you be more specific? I can help with data breaches, passwords, investments, or cryptocurrency.",
                "That's an interesting question! I specialize in cybersecurity and financial topics. Try asking about data protection, market analysis, or crypto trends.",
                "I'd love to help! I'm best at answering questions about cybersecurity threats, financial markets, and digital asset protection. What specific area interests you?",
                "Let me help you with that! I focus on cybersecurity and finance. You can ask about data breaches, investment strategies, or cryptocurrency insights."
            ];
            response = responses[Math.floor(Math.random() * responses.length)];
        }

        return {
            text: response,
            isBot: true,
            timestamp: new Date()
        };
    }

    handleQuickAction(action) {
        // Haptic feedback for mobile apps
        if (this.isMobileApp) {
            this.triggerHapticFeedback('light');
        }

        // Visual feedback
        const button = document.querySelector(`[data-action="${action}"]`);
        if (button) {
            button.classList.add('haptic-feedback');
            setTimeout(() => {
                button.classList.remove('haptic-feedback');
            }, 100);
        }

        let message = "";

        switch(action) {
            case 'cybersecurity':
                message = "What cybersecurity topic can I help you with?";
                break;
            case 'financial':
                message = "What financial or investment question do you have?";
                break;
            case 'data-breach':
                message = "I think I may have been affected by a data breach. What should I do?";
                break;
        }

        // Simulate user clicking the quick action
        const userMessage = {
            text: message,
            isBot: false,
            timestamp: new Date()
        };
        this.addMessage(userMessage);

        // Generate response
        setTimeout(() => {
            const botResponse = this.generateResponse(message);
            this.addMessage(botResponse);
        }, 500);
    }

    handleResize() {
        const previousIsMobile = this.isMobile;
        const previousIsSmallMobile = this.isSmallMobile;

        this.isMobile = window.innerWidth <= 768;
        this.isSmallMobile = window.innerWidth <= 480;

        // Update chatbot positioning if screen size category changed
        if (previousIsMobile !== this.isMobile || previousIsSmallMobile !== this.isSmallMobile) {
            this.updateChatbotLayout();
        }

        // Ensure proper scrolling after resize
        if (this.isOpen) {
            setTimeout(() => this.scrollToBottom(), 100);
        }
    }

    updateChatbotLayout() {
        const chatbotWindow = document.getElementById('chatbot-window');
        const input = document.getElementById('chatbot-input');

        if (this.isSmallMobile) {
            // Update placeholder text for small screens
            if (input) {
                input.placeholder = "Ask me anything...";
            }
        } else {
            // Restore full placeholder text
            if (input) {
                input.placeholder = "Ask me about cybersecurity or finance...";
            }
        }

        // Force layout recalculation
        if (chatbotWindow) {
            chatbotWindow.style.display = 'none';
            chatbotWindow.offsetHeight; // Trigger reflow
            chatbotWindow.style.display = '';
        }
    }

    detectMobileApp() {
        // Detect if running in mobile app environment
        const standalone = window.navigator.standalone;
        const userAgent = window.navigator.userAgent.toLowerCase();
        const isIOS = /iphone|ipad|ipod/.test(userAgent);
        const isAndroid = /android/.test(userAgent);
        const isPWA = window.matchMedia('(display-mode: standalone)').matches;

        return standalone || isPWA || (isIOS && !userAgent.includes('safari')) ||
               (isAndroid && userAgent.includes('wv'));
    }

    initMobileAppFeatures() {
        if (this.isMobileApp) {
            document.body.classList.add('mobile-app-mode');

            // Prevent zoom on double tap
            let lastTouchEnd = 0;
            document.addEventListener('touchend', (event) => {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);

            // Handle safe area insets
            this.handleSafeAreaInsets();
        }
    }

    bindMobileAppEvents() {
        const chatbotWindow = document.getElementById('chatbot-window');
        const chatbotToggle = document.getElementById('chatbot-toggle');

        // Touch events for gestures
        chatbotWindow.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: true });
        chatbotWindow.addEventListener('touchmove', (e) => this.handleTouchMove(e), { passive: true });
        chatbotWindow.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: true });

        // Haptic feedback simulation
        chatbotToggle.addEventListener('touchstart', () => this.triggerHapticFeedback('light'));

        // Handle app state changes
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());

        // Handle keyboard events on mobile
        if (this.isMobile) {
            window.addEventListener('resize', () => this.handleKeyboardToggle());
        }
    }

    handleTouchStart(e) {
        this.touchStartY = e.touches[0].clientY;
        this.isScrolling = false;
    }

    handleTouchMove(e) {
        if (!this.touchStartY) return;

        this.touchEndY = e.touches[0].clientY;
        const diff = this.touchStartY - this.touchEndY;

        // Detect if user is scrolling
        if (Math.abs(diff) > 10) {
            this.isScrolling = true;
        }
    }

    handleTouchEnd(e) {
        if (!this.touchStartY || !this.touchEndY || this.isScrolling) return;

        const diff = this.touchStartY - this.touchEndY;
        const chatbotWindow = document.getElementById('chatbot-window');

        // Swipe down to minimize (if at top of chat)
        if (diff < -50 && this.isAtTopOfChat()) {
            this.addSwipeAnimation('down');
            setTimeout(() => this.closeChatbot(), 200);
        }

        // Reset touch positions
        this.touchStartY = 0;
        this.touchEndY = 0;
    }

    isAtTopOfChat() {
        const messagesContainer = document.getElementById('chatbot-messages');
        return messagesContainer && messagesContainer.scrollTop === 0;
    }

    addSwipeAnimation(direction) {
        const chatbotWindow = document.getElementById('chatbot-window');
        chatbotWindow.classList.add(`swipe-${direction}`);
        setTimeout(() => {
            chatbotWindow.classList.remove(`swipe-${direction}`);
        }, 200);
    }

    triggerHapticFeedback(type = 'light') {
        // Trigger haptic feedback if available
        if (navigator.vibrate) {
            switch(type) {
                case 'light':
                    navigator.vibrate(10);
                    break;
                case 'medium':
                    navigator.vibrate(20);
                    break;
                case 'heavy':
                    navigator.vibrate(50);
                    break;
            }
        }

        // Visual feedback for devices without haptic
        const element = document.getElementById('chatbot-toggle');
        element.classList.add('haptic-feedback');
        setTimeout(() => {
            element.classList.remove('haptic-feedback');
        }, 100);
    }

    handleVisibilityChange() {
        if (document.hidden) {
            // App went to background
            this.pauseAnimations();
        } else {
            // App came to foreground
            this.resumeAnimations();
        }
    }

    pauseAnimations() {
        const typingDots = document.querySelectorAll('.typing-dots span');
        typingDots.forEach(dot => {
            dot.style.animationPlayState = 'paused';
        });
    }

    resumeAnimations() {
        const typingDots = document.querySelectorAll('.typing-dots span');
        typingDots.forEach(dot => {
            dot.style.animationPlayState = 'running';
        });
    }

    handleKeyboardToggle() {
        // Detect virtual keyboard on mobile
        const initialHeight = window.innerHeight;

        setTimeout(() => {
            const currentHeight = window.innerHeight;
            const heightDiff = initialHeight - currentHeight;

            if (heightDiff > 150) {
                // Keyboard is open
                this.adjustForKeyboard(true);
            } else {
                // Keyboard is closed
                this.adjustForKeyboard(false);
            }
        }, 100);
    }

    adjustForKeyboard(isOpen) {
        const chatbotWindow = document.getElementById('chatbot-window');
        const chatbotContainer = document.getElementById('chatbot-container');

        if (isOpen) {
            chatbotWindow.style.height = 'calc(100vh - 300px)';
            chatbotContainer.style.bottom = '10px';
        } else {
            chatbotWindow.style.height = '';
            chatbotContainer.style.bottom = '';
        }

        // Scroll to bottom after keyboard adjustment
        setTimeout(() => this.scrollToBottom(), 100);
    }

    handleSafeAreaInsets() {
        // Handle iPhone X+ safe areas
        const chatbotContainer = document.getElementById('chatbot-container');
        const chatbotWindow = document.getElementById('chatbot-window');

        if (this.isMobileApp) {
            chatbotContainer.classList.add('mobile-app-mode');
            chatbotWindow.classList.add('mobile-app-mode');
        }
    }

    toggleFullScreenMode() {
        const chatbotWindow = document.getElementById('chatbot-window');
        const chatbotContainer = document.getElementById('chatbot-container');

        if (this.isMobile && this.isOpen) {
            chatbotWindow.classList.toggle('mobile-app-mode');
            chatbotContainer.classList.toggle('mobile-app-mode');
        }
    }

    scrollToBottom() {
        const messagesContainer = document.getElementById('chatbot-messages');
        if (messagesContainer) {
            // Use smooth scrolling for better mobile experience
            messagesContainer.scrollTo({
                top: messagesContainer.scrollHeight,
                behavior: 'smooth'
            });
        }
    }
}

// Initialize chatbot when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new CyberFinanceChatbot();
});
