// Contact Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize contact form
    initializeContactForm();
    
    // Initialize FAQ interactions
    initializeFAQ();
});

// Initialize Contact Form
function initializeContactForm() {
    const contactForm = document.getElementById('contact-form');
    const formMessage = document.getElementById('contact-form-message');

    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(contactForm);
            const data = {
                firstName: formData.get('firstName'),
                lastName: formData.get('lastName'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                company: formData.get('company'),
                subject: formData.get('subject'),
                message: formData.get('message'),
                newsletter: formData.get('newsletter') === 'on'
            };

            // Validate form
            if (!validateContactForm(data)) {
                return;
            }

            // Show loading state
            showContactMessage('Sending message...', 'info');
            
            // Simulate API call
            setTimeout(() => {
                // Store message in localStorage (in real app, send to server)
                let messages = JSON.parse(localStorage.getItem('contactMessages') || '[]');
                
                const message = {
                    id: Date.now(),
                    ...data,
                    timestamp: new Date().toISOString(),
                    status: 'received'
                };
                
                messages.push(message);
                localStorage.setItem('contactMessages', JSON.stringify(messages));
                
                // If newsletter subscription is checked, add to subscribers
                if (data.newsletter) {
                    let subscribers = JSON.parse(localStorage.getItem('subscribers') || '[]');
                    if (!subscribers.includes(data.email)) {
                        subscribers.push(data.email);
                        localStorage.setItem('subscribers', JSON.stringify(subscribers));
                    }
                }
                
                showContactMessage('Thank you for your message! We\'ll get back to you within 24 hours.', 'success');
                contactForm.reset();
                
                // Send confirmation email simulation
                console.log('Contact form submitted:', message);
                
            }, 1500);
        });
    }
}

// Validate Contact Form
function validateContactForm(data) {
    const errors = [];
    
    // Required field validation
    if (!data.firstName.trim()) {
        errors.push('First name is required');
    }
    
    if (!data.lastName.trim()) {
        errors.push('Last name is required');
    }
    
    if (!data.email.trim()) {
        errors.push('Email address is required');
    } else if (!isValidEmail(data.email)) {
        errors.push('Please enter a valid email address');
    }
    
    if (!data.subject) {
        errors.push('Please select a subject');
    }
    
    if (!data.message.trim()) {
        errors.push('Message is required');
    } else if (data.message.trim().length < 10) {
        errors.push('Message must be at least 10 characters long');
    }
    
    // Phone validation (if provided)
    if (data.phone && !isValidPhone(data.phone)) {
        errors.push('Please enter a valid phone number');
    }
    
    if (errors.length > 0) {
        showContactMessage(errors.join('<br>'), 'error');
        return false;
    }
    
    return true;
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Phone validation
function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    return phoneRegex.test(cleanPhone);
}

// Show contact form message
function showContactMessage(message, type) {
    const formMessage = document.getElementById('contact-form-message');
    if (formMessage) {
        formMessage.innerHTML = message;
        formMessage.className = `form-message ${type}`;
        formMessage.style.display = 'block';
        
        // Auto-hide success messages after 10 seconds
        if (type === 'success') {
            setTimeout(() => {
                formMessage.style.display = 'none';
            }, 10000);
        }
        
        // Scroll to message
        formMessage.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
}

// Initialize FAQ interactions
function initializeFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');
        
        if (question && answer) {
            // Initially hide answers (optional - can be shown by default)
            // answer.style.display = 'none';
            
            question.addEventListener('click', function() {
                // Toggle FAQ item
                const isExpanded = item.classList.contains('expanded');
                
                // Close all other FAQ items (accordion style)
                faqItems.forEach(otherItem => {
                    if (otherItem !== item) {
                        otherItem.classList.remove('expanded');
                        const otherAnswer = otherItem.querySelector('.faq-answer');
                        if (otherAnswer) {
                            otherAnswer.style.maxHeight = null;
                        }
                    }
                });
                
                // Toggle current item
                if (isExpanded) {
                    item.classList.remove('expanded');
                    answer.style.maxHeight = null;
                } else {
                    item.classList.add('expanded');
                    answer.style.maxHeight = answer.scrollHeight + 'px';
                }
            });
            
            // Add hover effects
            question.style.cursor = 'pointer';
            question.addEventListener('mouseenter', function() {
                question.style.color = 'var(--primary-color)';
            });
            
            question.addEventListener('mouseleave', function() {
                if (!item.classList.contains('expanded')) {
                    question.style.color = '';
                }
            });
        }
    });
}

// Auto-fill form from URL parameters (useful for specific inquiries)
function autoFillFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    
    const subject = urlParams.get('subject');
    const message = urlParams.get('message');
    
    if (subject) {
        const subjectSelect = document.getElementById('subject');
        if (subjectSelect) {
            subjectSelect.value = subject;
        }
    }
    
    if (message) {
        const messageTextarea = document.getElementById('message');
        if (messageTextarea) {
            messageTextarea.value = decodeURIComponent(message);
        }
    }
}

// Call auto-fill on page load
autoFillFromURL();

// Form field enhancements
function enhanceFormFields() {
    // Add character counter to message field
    const messageField = document.getElementById('message');
    if (messageField) {
        const maxLength = 1000;
        messageField.setAttribute('maxlength', maxLength);
        
        const counter = document.createElement('div');
        counter.className = 'character-counter';
        counter.style.fontSize = 'var(--font-size-sm)';
        counter.style.color = 'var(--gray-500)';
        counter.style.textAlign = 'right';
        counter.style.marginTop = '0.25rem';
        
        messageField.parentNode.appendChild(counter);
        
        function updateCounter() {
            const remaining = maxLength - messageField.value.length;
            counter.textContent = `${remaining} characters remaining`;
            
            if (remaining < 50) {
                counter.style.color = 'var(--warning-color)';
            } else if (remaining < 20) {
                counter.style.color = 'var(--danger-color)';
            } else {
                counter.style.color = 'var(--gray-500)';
            }
        }
        
        messageField.addEventListener('input', updateCounter);
        updateCounter();
    }
    
    // Add real-time email validation
    const emailField = document.getElementById('email');
    if (emailField) {
        emailField.addEventListener('blur', function() {
            const email = emailField.value.trim();
            if (email && !isValidEmail(email)) {
                emailField.style.borderColor = 'var(--danger-color)';
                
                // Add error message if not already present
                let errorMsg = emailField.parentNode.querySelector('.field-error');
                if (!errorMsg) {
                    errorMsg = document.createElement('div');
                    errorMsg.className = 'field-error';
                    errorMsg.style.color = 'var(--danger-color)';
                    errorMsg.style.fontSize = 'var(--font-size-sm)';
                    errorMsg.style.marginTop = '0.25rem';
                    emailField.parentNode.appendChild(errorMsg);
                }
                errorMsg.textContent = 'Please enter a valid email address';
            } else {
                emailField.style.borderColor = '';
                const errorMsg = emailField.parentNode.querySelector('.field-error');
                if (errorMsg) {
                    errorMsg.remove();
                }
            }
        });
    }
}

// Initialize form enhancements
enhanceFormFields();

// Contact form analytics (for tracking form interactions)
function trackFormInteraction(action, field) {
    // In a real application, you would send this to your analytics service
    console.log('Form interaction:', { action, field, timestamp: new Date().toISOString() });
}

// Add event listeners for form analytics
document.querySelectorAll('#contact-form input, #contact-form select, #contact-form textarea').forEach(field => {
    field.addEventListener('focus', () => trackFormInteraction('focus', field.name));
    field.addEventListener('blur', () => trackFormInteraction('blur', field.name));
});

// Export functions for use in other scripts
window.contactPage = {
    validateContactForm,
    showContactMessage,
    isValidEmail,
    isValidPhone,
    trackFormInteraction
};
