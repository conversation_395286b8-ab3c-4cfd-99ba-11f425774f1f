// Cryptocurrency Page Specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize crypto data
    loadMarketStats();
    loadCryptoData();
    
    // Refresh data every 30 seconds
    setInterval(() => {
        loadMarketStats();
        loadCryptoData();
    }, 30000);
    
    // Add click handlers for crypto rows
    initializeCryptoRowHandlers();
});

// Load Market Statistics
async function loadMarketStats() {
    try {
        // In a real application, you would fetch from CoinGecko or similar API
        // For demo purposes, using mock data with some randomization
        const baseStats = {
            totalMarketCap: 1200000000000, // $1.2T
            totalVolume: 45600000000, // $45.6B
            btcDominance: 52.3,
            fearGreedIndex: 65
        };

        // Add some random variation to simulate live data
        const marketCapVariation = (Math.random() - 0.5) * 0.1;
        const volumeVariation = (Math.random() - 0.5) * 0.2;
        const dominanceVariation = (Math.random() - 0.5) * 2;
        const fearGreedVariation = (Math.random() - 0.5) * 10;

        const currentStats = {
            totalMarketCap: baseStats.totalMarketCap * (1 + marketCapVariation),
            totalVolume: baseStats.totalVolume * (1 + volumeVariation),
            btcDominance: baseStats.btcDominance + dominanceVariation,
            fearGreedIndex: Math.max(0, Math.min(100, baseStats.fearGreedIndex + fearGreedVariation))
        };

        updateMarketStats(currentStats);
    } catch (error) {
        console.error('Error loading market stats:', error);
    }
}

// Update Market Statistics Display
function updateMarketStats(stats) {
    const elements = {
        totalMarketCap: document.getElementById('total-market-cap'),
        totalVolume: document.getElementById('total-volume'),
        btcDominance: document.getElementById('btc-dominance'),
        fearGreedIndex: document.getElementById('fear-greed')
    };

    if (elements.totalMarketCap) {
        elements.totalMarketCap.textContent = formatMarketCap(stats.totalMarketCap);
    }
    
    if (elements.totalVolume) {
        elements.totalVolume.textContent = formatMarketCap(stats.totalVolume);
    }
    
    if (elements.btcDominance) {
        elements.btcDominance.textContent = `${stats.btcDominance.toFixed(1)}%`;
    }
    
    if (elements.fearGreedIndex) {
        elements.fearGreedIndex.textContent = Math.round(stats.fearGreedIndex);
        
        // Update color based on fear/greed level
        const fearGreedElement = elements.fearGreedIndex.parentElement;
        fearGreedElement.className = 'stat-content';
        
        if (stats.fearGreedIndex < 25) {
            fearGreedElement.classList.add('extreme-fear');
        } else if (stats.fearGreedIndex < 45) {
            fearGreedElement.classList.add('fear');
        } else if (stats.fearGreedIndex < 55) {
            fearGreedElement.classList.add('neutral');
        } else if (stats.fearGreedIndex < 75) {
            fearGreedElement.classList.add('greed');
        } else {
            fearGreedElement.classList.add('extreme-greed');
        }
    }
}

// Load Cryptocurrency Data
async function loadCryptoData() {
    try {
        // Using CoinGecko API for real data
        const response = await fetch('https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=10&page=1&sparkline=false&price_change_percentage=24h');
        const data = await response.json();
        
        updateCryptoTable(data);
    } catch (error) {
        console.error('Error loading crypto data:', error);
        // Fallback to mock data
        const mockData = [
            {
                id: 'bitcoin',
                name: 'Bitcoin',
                symbol: 'BTC',
                current_price: 43250.67,
                price_change_percentage_24h: 2.34,
                total_volume: 15200000000,
                market_cap: 847500000000,
                market_cap_rank: 1
            },
            {
                id: 'ethereum',
                name: 'Ethereum',
                symbol: 'ETH',
                current_price: 2587.43,
                price_change_percentage_24h: -1.23,
                total_volume: 8700000000,
                market_cap: 311200000000,
                market_cap_rank: 2
            },
            {
                id: 'binancecoin',
                name: 'BNB',
                symbol: 'BNB',
                current_price: 315.67,
                price_change_percentage_24h: 0.87,
                total_volume: 1200000000,
                market_cap: 47300000000,
                market_cap_rank: 3
            },
            {
                id: 'solana',
                name: 'Solana',
                symbol: 'SOL',
                current_price: 98.45,
                price_change_percentage_24h: 5.67,
                total_volume: 2100000000,
                market_cap: 42800000000,
                market_cap_rank: 4
            },
            {
                id: 'cardano',
                name: 'Cardano',
                symbol: 'ADA',
                current_price: 0.4521,
                price_change_percentage_24h: 3.45,
                total_volume: 456000000,
                market_cap: 15900000000,
                market_cap_rank: 5
            }
        ];
        
        updateCryptoTable(mockData);
    }
}

// Update Cryptocurrency Table
function updateCryptoTable(cryptoData) {
    const cryptoRows = document.querySelectorAll('.crypto-row');
    
    cryptoRows.forEach((row, index) => {
        if (cryptoData[index]) {
            const crypto = cryptoData[index];
            
            // Update price
            const priceElement = row.querySelector('.crypto-price');
            if (priceElement) {
                priceElement.textContent = formatCryptoPrice(crypto.current_price);
            }
            
            // Update 24h change
            const changeElement = row.querySelector('.crypto-change');
            if (changeElement) {
                const change = crypto.price_change_percentage_24h;
                changeElement.textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`;
                changeElement.className = `crypto-change ${change >= 0 ? 'positive' : 'negative'}`;
            }
            
            // Update volume
            const volumeElement = row.querySelector('.crypto-volume');
            if (volumeElement) {
                volumeElement.textContent = formatMarketCap(crypto.total_volume);
            }
            
            // Update market cap
            const marketCapElement = row.querySelector('.crypto-market-cap');
            if (marketCapElement) {
                marketCapElement.textContent = formatMarketCap(crypto.market_cap);
            }
        }
    });
}

// Initialize click handlers for crypto rows
function initializeCryptoRowHandlers() {
    const cryptoRows = document.querySelectorAll('.crypto-row');
    
    cryptoRows.forEach(row => {
        row.addEventListener('click', function() {
            const cryptoId = this.getAttribute('data-crypto');
            if (cryptoId) {
                showCryptoDetails(cryptoId);
            }
        });
    });
}

// Show cryptocurrency details (placeholder function)
function showCryptoDetails(cryptoId) {
    // In a real application, this would open a detailed view or modal
    console.log(`Showing details for ${cryptoId}`);
    
    // For demo, show an alert
    const cryptoName = document.querySelector(`[data-crypto="${cryptoId}"] .crypto-title`).textContent;
    alert(`Detailed view for ${cryptoName} would open here in a real application.`);
}

// Format cryptocurrency price
function formatCryptoPrice(price) {
    if (price < 0.01) {
        return `$${price.toFixed(6)}`;
    } else if (price < 1) {
        return `$${price.toFixed(4)}`;
    } else if (price < 100) {
        return `$${price.toFixed(2)}`;
    } else {
        return `$${price.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        })}`;
    }
}

// Format market cap and volume
function formatMarketCap(value) {
    if (value >= 1000000000000) {
        return `$${(value / 1000000000000).toFixed(1)}T`;
    } else if (value >= 1000000000) {
        return `$${(value / 1000000000).toFixed(1)}B`;
    } else if (value >= 1000000) {
        return `$${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
        return `$${(value / 1000).toFixed(1)}K`;
    } else {
        return `$${value.toFixed(2)}`;
    }
}

// Crypto price alert system
function setCryptoPriceAlert(cryptoId, targetPrice, direction) {
    // Store alert in localStorage (in real app, send to server)
    const alerts = JSON.parse(localStorage.getItem('cryptoAlerts') || '[]');
    
    const alert = {
        id: Date.now(),
        cryptoId,
        targetPrice,
        direction, // 'above' or 'below'
        created: new Date().toISOString()
    };
    
    alerts.push(alert);
    localStorage.setItem('cryptoAlerts', JSON.stringify(alerts));
    
    console.log(`Price alert set for ${cryptoId} at $${targetPrice} (${direction})`);
}

// Check price alerts (would be called periodically)
function checkPriceAlerts() {
    const alerts = JSON.parse(localStorage.getItem('cryptoAlerts') || '[]');
    
    alerts.forEach(alert => {
        // In real app, compare with current prices and trigger notifications
        console.log('Checking alert:', alert);
    });
}

// Add CSS for fear/greed indicator colors
const fearGreedStyles = `
<style>
.extreme-fear .stat-value { color: var(--danger-color) !important; }
.fear .stat-value { color: #ff6b35 !important; }
.neutral .stat-value { color: var(--warning-color) !important; }
.greed .stat-value { color: #90ee90 !important; }
.extreme-greed .stat-value { color: var(--success-color) !important; }

.crypto-row:hover {
    background-color: var(--gray-50) !important;
}

.crypto-price-alert {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background-color: var(--primary-color);
    color: var(--white);
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    transform: translateY(100px);
    transition: transform 0.3s ease;
    z-index: 1001;
    max-width: 300px;
}

.crypto-price-alert.show {
    transform: translateY(0);
}

.crypto-price-alert .close-btn {
    background: none;
    border: none;
    color: var(--white);
    font-size: 1.2rem;
    cursor: pointer;
    float: right;
    margin-left: 1rem;
}

.crypto-calculator {
    background-color: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    margin-top: 2rem;
}

.calculator-input {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.calculator-input input,
.calculator-input select {
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
}

.calculator-result {
    padding: 1rem;
    background-color: var(--gray-100);
    border-radius: var(--border-radius);
    font-weight: 600;
    text-align: center;
}
</style>
`;

// Inject fear/greed styles
document.head.insertAdjacentHTML('beforeend', fearGreedStyles);

// Export functions for use in other scripts
window.cryptocurrency = {
    loadMarketStats,
    loadCryptoData,
    setCryptoPriceAlert,
    checkPriceAlerts,
    showCryptoDetails
};
