// Main JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    // Mobile Navigation Toggle
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');

    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navToggle.classList.toggle('active');
            navMenu.classList.toggle('active');
        });

        // Close mobile menu when clicking on a link
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                navToggle.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
    }

    // Newsletter Form Submission
    const newsletterForm = document.getElementById('newsletter-form');
    const formMessage = document.getElementById('form-message');

    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            
            // Basic email validation
            if (!isValidEmail(email)) {
                showMessage('Please enter a valid email address.', 'error');
                return;
            }

            // Simulate API call
            showMessage('Subscribing...', 'info');
            
            setTimeout(() => {
                // Store email in localStorage (in real app, send to server)
                let subscribers = JSON.parse(localStorage.getItem('subscribers') || '[]');
                
                if (subscribers.includes(email)) {
                    showMessage('You are already subscribed!', 'error');
                } else {
                    subscribers.push(email);
                    localStorage.setItem('subscribers', JSON.stringify(subscribers));
                    showMessage('Successfully subscribed! Thank you for joining us.', 'success');
                    newsletterForm.reset();
                }
            }, 1000);
        });
    }

    // Load Market Data
    loadStockData();
    loadCryptoData();

    // Refresh market data every 30 seconds
    setInterval(() => {
        loadStockData();
        loadCryptoData();
    }, 30000);

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Active navigation link highlighting
    updateActiveNavLink();
    window.addEventListener('scroll', updateActiveNavLink);
});

// Email validation function
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Show form message
function showMessage(message, type) {
    const formMessage = document.getElementById('form-message');
    if (formMessage) {
        formMessage.textContent = message;
        formMessage.className = `form-message ${type}`;
        formMessage.style.display = 'block';
        
        if (type === 'success') {
            setTimeout(() => {
                formMessage.style.display = 'none';
            }, 5000);
        }
    }
}

// Load Stock Market Data
async function loadStockData() {
    const stockContainer = document.getElementById('stock-data');
    if (!stockContainer) return;

    try {
        // Using Alpha Vantage API (free tier) - replace with your API key
        // For demo purposes, using mock data
        const mockStockData = [
            { symbol: 'AAPL', price: 175.43, change: 2.15, changePercent: 1.24 },
            { symbol: 'GOOGL', price: 2847.63, change: -15.32, changePercent: -0.53 },
            { symbol: 'MSFT', price: 338.11, change: 4.87, changePercent: 1.46 },
            { symbol: 'TSLA', price: 248.50, change: -3.21, changePercent: -1.27 }
        ];

        displayStockData(mockStockData);
    } catch (error) {
        console.error('Error loading stock data:', error);
        stockContainer.innerHTML = '<div class="error">Unable to load stock data</div>';
    }
}

// Display Stock Data
function displayStockData(data) {
    const stockContainer = document.getElementById('stock-data');
    if (!stockContainer) return;

    const html = data.map(stock => `
        <div class="stock-item">
            <div class="stock-symbol">${stock.symbol}</div>
            <div class="stock-price">$${stock.price.toFixed(2)}</div>
            <div class="stock-change ${stock.change >= 0 ? 'positive' : 'negative'}">
                ${stock.change >= 0 ? '+' : ''}${stock.change.toFixed(2)} (${stock.changePercent.toFixed(2)}%)
            </div>
        </div>
    `).join('');

    stockContainer.innerHTML = html;
}

// Load Cryptocurrency Data
async function loadCryptoData() {
    const cryptoContainer = document.getElementById('crypto-data');
    if (!cryptoContainer) return;

    try {
        // Using CoinGecko API (free)
        const response = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=bitcoin,ethereum,cardano,polkadot&vs_currencies=usd&include_24hr_change=true');
        const data = await response.json();
        
        const cryptoData = [
            {
                name: 'Bitcoin',
                symbol: 'BTC',
                price: data.bitcoin.usd,
                change: data.bitcoin.usd_24h_change
            },
            {
                name: 'Ethereum',
                symbol: 'ETH',
                price: data.ethereum.usd,
                change: data.ethereum.usd_24h_change
            },
            {
                name: 'Cardano',
                symbol: 'ADA',
                price: data.cardano.usd,
                change: data.cardano.usd_24h_change
            },
            {
                name: 'Polkadot',
                symbol: 'DOT',
                price: data.polkadot.usd,
                change: data.polkadot.usd_24h_change
            }
        ];

        displayCryptoData(cryptoData);
    } catch (error) {
        console.error('Error loading crypto data:', error);
        // Fallback to mock data
        const mockCryptoData = [
            { name: 'Bitcoin', symbol: 'BTC', price: 43250.67, change: 2.34 },
            { name: 'Ethereum', symbol: 'ETH', price: 2587.43, change: -1.23 },
            { name: 'Cardano', symbol: 'ADA', price: 0.4521, change: 3.45 },
            { name: 'Polkadot', symbol: 'DOT', price: 7.23, change: -0.87 }
        ];
        displayCryptoData(mockCryptoData);
    }
}

// Display Crypto Data
function displayCryptoData(data) {
    const cryptoContainer = document.getElementById('crypto-data');
    if (!cryptoContainer) return;

    const html = data.map(crypto => `
        <div class="crypto-item">
            <div class="crypto-info">
                <div class="crypto-name">${crypto.name}</div>
                <div class="crypto-symbol">${crypto.symbol}</div>
            </div>
            <div class="crypto-price">$${crypto.price.toFixed(crypto.price < 1 ? 4 : 2)}</div>
            <div class="crypto-change ${crypto.change >= 0 ? 'positive' : 'negative'}">
                ${crypto.change >= 0 ? '+' : ''}${crypto.change.toFixed(2)}%
            </div>
        </div>
    `).join('');

    cryptoContainer.innerHTML = html;
}

// Update active navigation link based on scroll position
function updateActiveNavLink() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (window.pageYOffset >= sectionTop - 200) {
            current = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
}

// Utility function to format numbers
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// Add CSS for market data styling
const marketDataStyles = `
<style>
.stock-item, .crypto-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.stock-item:last-child, .crypto-item:last-child {
    border-bottom: none;
}

.stock-symbol, .crypto-name {
    font-weight: 600;
    color: var(--dark-color);
}

.crypto-info {
    display: flex;
    flex-direction: column;
}

.crypto-symbol {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.stock-price, .crypto-price {
    font-weight: 600;
    color: var(--dark-color);
}

.stock-change, .crypto-change {
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.positive {
    color: var(--success-color);
}

.negative {
    color: var(--danger-color);
}

.error {
    color: var(--danger-color);
    text-align: center;
    padding: 2rem;
}

.form-message.info {
    background-color: var(--primary-color);
    color: var(--white);
}
</style>
`;

// Inject market data styles
document.head.insertAdjacentHTML('beforeend', marketDataStyles);
