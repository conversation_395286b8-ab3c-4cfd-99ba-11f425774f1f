// Stock Market Page Specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize stock market data
    loadMarketIndices();
    loadTopStocks();
    
    // Refresh data every 30 seconds
    setInterval(() => {
        loadMarketIndices();
        loadTopStocks();
    }, 30000);
});

// Load Market Indices Data
async function loadMarketIndices() {
    try {
        // In a real application, you would fetch from a financial API
        // For demo purposes, using mock data with some randomization
        const indices = [
            {
                id: 'spx',
                name: 'S&P 500',
                symbol: 'SPX',
                basePrice: 4567.89,
                baseChange: 23.45
            },
            {
                id: 'dji',
                name: '<PERSON>',
                symbol: 'DJ<PERSON>',
                basePrice: 35421.67,
                baseChange: -145.23
            },
            {
                id: 'nasdaq',
                name: 'NASDAQ',
                symbol: 'IXIC',
                basePrice: 14789.32,
                baseChange: 67.89
            }
        ];

        indices.forEach(index => {
            // Add some random variation to simulate live data
            const priceVariation = (Math.random() - 0.5) * 10;
            const changeVariation = (Math.random() - 0.5) * 5;
            
            const currentPrice = index.basePrice + priceVariation;
            const currentChange = index.baseChange + changeVariation;
            const changePercent = (currentChange / (currentPrice - currentChange)) * 100;

            updateIndexDisplay(index.id, currentPrice, currentChange, changePercent);
        });
    } catch (error) {
        console.error('Error loading market indices:', error);
    }
}

// Update Index Display
function updateIndexDisplay(indexId, price, change, changePercent) {
    const priceElement = document.getElementById(`${indexId}-price`);
    const changeElement = document.getElementById(`${indexId}-change`);

    if (priceElement && changeElement) {
        priceElement.textContent = formatPrice(price);
        
        const changeText = `${change >= 0 ? '+' : ''}${change.toFixed(2)} (${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%)`;
        changeElement.textContent = changeText;
        
        // Update color based on change
        changeElement.className = `index-change ${change >= 0 ? 'positive' : 'negative'}`;
    }
}

// Load Top Stocks Data
async function loadTopStocks() {
    try {
        // Mock stock data - in real app, fetch from financial API
        const stocks = [
            {
                symbol: 'AAPL',
                name: 'Apple Inc.',
                basePrice: 175.43,
                baseChange: 2.15,
                volume: '45.2M'
            },
            {
                symbol: 'MSFT',
                name: 'Microsoft Corp.',
                basePrice: 338.11,
                baseChange: 4.87,
                volume: '28.7M'
            },
            {
                symbol: 'GOOGL',
                name: 'Alphabet Inc.',
                basePrice: 2847.63,
                baseChange: -15.32,
                volume: '1.2M'
            },
            {
                symbol: 'TSLA',
                name: 'Tesla Inc.',
                basePrice: 248.50,
                baseChange: -3.21,
                volume: '89.4M'
            }
        ];

        // Update stock cards with live-like data
        const stockCards = document.querySelectorAll('.stock-card');
        stockCards.forEach((card, index) => {
            if (stocks[index]) {
                const stock = stocks[index];
                
                // Add some random variation
                const priceVariation = (Math.random() - 0.5) * 5;
                const changeVariation = (Math.random() - 0.5) * 2;
                
                const currentPrice = stock.basePrice + priceVariation;
                const currentChange = stock.baseChange + changeVariation;
                const changePercent = (currentChange / (currentPrice - currentChange)) * 100;

                updateStockCard(card, {
                    ...stock,
                    price: currentPrice,
                    change: currentChange,
                    changePercent: changePercent
                });
            }
        });
    } catch (error) {
        console.error('Error loading stock data:', error);
    }
}

// Update Stock Card
function updateStockCard(card, stock) {
    const priceElement = card.querySelector('.stock-price');
    const changeElement = card.querySelector('.stock-change');

    if (priceElement && changeElement) {
        priceElement.textContent = `$${stock.price.toFixed(2)}`;
        
        const changeText = `${stock.change >= 0 ? '+' : ''}${stock.change.toFixed(2)} (${stock.changePercent >= 0 ? '+' : ''}${stock.changePercent.toFixed(2)}%)`;
        changeElement.textContent = changeText;
        
        // Update color based on change
        changeElement.className = `stock-change ${stock.change >= 0 ? 'positive' : 'negative'}`;
    }
}

// Format price based on value
function formatPrice(price) {
    if (price >= 1000) {
        return price.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    } else {
        return price.toFixed(2);
    }
}

// Stock Search Functionality (if implemented)
function initializeStockSearch() {
    const searchInput = document.getElementById('stock-search');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const query = e.target.value.toLowerCase();
            // Implement stock search logic here
            console.log('Searching for:', query);
        });
    }
}

// Market Status Indicator
function updateMarketStatus() {
    const now = new Date();
    const currentHour = now.getHours();
    const currentDay = now.getDay();
    
    // Market hours: Monday-Friday, 9:30 AM - 4:00 PM EST
    const isWeekday = currentDay >= 1 && currentDay <= 5;
    const isMarketHours = currentHour >= 9 && currentHour < 16;
    const isMarketOpen = isWeekday && isMarketHours;
    
    const statusElement = document.getElementById('market-status');
    if (statusElement) {
        statusElement.textContent = isMarketOpen ? 'Market Open' : 'Market Closed';
        statusElement.className = `market-status ${isMarketOpen ? 'open' : 'closed'}`;
    }
}

// Initialize market status
updateMarketStatus();

// Update market status every minute
setInterval(updateMarketStatus, 60000);

// Add market status styles
const marketStatusStyles = `
<style>
.market-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-left: 1rem;
}

.market-status.open {
    background-color: var(--success-color);
    color: var(--white);
}

.market-status.closed {
    background-color: var(--danger-color);
    color: var(--white);
}

.stock-item, .crypto-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.stock-item:last-child, .crypto-item:last-child {
    border-bottom: none;
}

.positive {
    color: var(--success-color);
}

.negative {
    color: var(--danger-color);
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--gray-300);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.price-alert {
    position: fixed;
    top: 5rem;
    right: 1rem;
    background-color: var(--primary-color);
    color: var(--white);
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 1001;
}

.price-alert.show {
    transform: translateX(0);
}

.price-alert .close-btn {
    background: none;
    border: none;
    color: var(--white);
    font-size: 1.2rem;
    cursor: pointer;
    float: right;
    margin-left: 1rem;
}
</style>
`;

// Inject market status styles
document.head.insertAdjacentHTML('beforeend', marketStatusStyles);

// Price Alert System (basic implementation)
function showPriceAlert(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'price-alert';
    alertDiv.innerHTML = `
        <button class="close-btn" onclick="this.parentElement.remove()">&times;</button>
        <div>${message}</div>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Show alert
    setTimeout(() => {
        alertDiv.classList.add('show');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 5000);
}

// Example usage of price alert (can be triggered by price changes)
// showPriceAlert('AAPL has reached your target price of $180.00');

// Export functions for use in other scripts
window.stockMarket = {
    loadMarketIndices,
    loadTopStocks,
    showPriceAlert,
    updateMarketStatus
};
